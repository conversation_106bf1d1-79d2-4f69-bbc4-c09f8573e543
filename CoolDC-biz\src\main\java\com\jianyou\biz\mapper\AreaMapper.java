package com.jianyou.biz.mapper;

import cn.hutool.core.util.StrUtil;
import com.jianyou.biz.domain.AreaDO;
import com.jianyou.biz.domain.vo.AreaVO;
import com.jianyou.common.core.mapper.BaseMapperPlus;

import java.util.ArrayList;
import java.util.List;

/**
 * 区域Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface AreaMapper extends BaseMapperPlus<AreaMapper, AreaDO, AreaVO> {

    /**
     * 获取区域全路径名称
     * 格式为: 父名称/子名称/...
     *
     * @param areaId 区域ID
     * @return 区域全路径名称
     */
    default String getAreaFullPath(String areaId) {
        if (StrUtil.isEmpty(areaId)) {
            return "";
        }

        List<String> pathNames = new ArrayList<>();
        String currentAreaId = areaId;

        // 递归查询父区域，直到根区域（parentId为"0"的区域）
        while (StrUtil.isNotEmpty(currentAreaId) && !"0".equals(currentAreaId)) {
            AreaVO area = selectVoById(currentAreaId);
            if (area == null) {
                break;
            }

            // 将当前区域名称添加到路径列表的开头
            pathNames.add(0, area.getName());

            // 继续查询父区域
            currentAreaId = area.getParentId();
        }

        // 将路径列表中的名称用"/"连接起来
        return String.join("/", pathNames);
    }

}
