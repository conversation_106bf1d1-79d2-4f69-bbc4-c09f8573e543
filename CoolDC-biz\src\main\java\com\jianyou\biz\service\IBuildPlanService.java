package com.jianyou.biz.service;

import com.jianyou.biz.domain.vo.BuildPlanVO;
import com.jianyou.biz.domain.bo.BuildPlanBO;
import com.jianyou.common.core.domain.BaseOptionsVO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 施工计划Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IBuildPlanService {


    /**
     * 查询施工计划列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link BuildPlanVO }>
     */
    TableDataInfo<BuildPlanVO> queryPageList(BuildPlanBO bo, PageQuery pageQuery);

    /**
     * 查询施工计划
     *
     * @param id id
     * @return {@link BuildPlanVO }
     */
     BuildPlanVO queryById(String id);

    /**
     * 新增施工计划
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(BuildPlanBO bo);

    /**
     * 修改施工计划
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(BuildPlanBO bo);

    /**
     * 校验并批量删除施工计划信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 施工计划选项
     */
    List<BaseOptionsVO> option(BuildPlanBO bo);

    /**
     * 计划变更
     */
    void planChange(String buildPlanId, String changeStatus);
}
