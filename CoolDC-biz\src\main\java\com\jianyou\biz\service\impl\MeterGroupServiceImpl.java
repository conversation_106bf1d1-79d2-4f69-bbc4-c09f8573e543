package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.MeterGroupDO;
import com.jianyou.biz.mapper.MeterGroupMapper;
import com.jianyou.biz.service.IMeterGroupService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 表具分组关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RequiredArgsConstructor
@Service
public class MeterGroupServiceImpl implements IMeterGroupService {

    private final MeterGroupMapper meterGroupMapper;

    /**
     * 根据分组ID查询关联的表具ID列表
     *
     * @param groupId 分组ID
     * @return 表具ID列表
     */
    @Override
    public List<String> getMeterIdsByGroupId(String groupId) {
        List<MeterGroupDO> meterGroups = meterGroupMapper.selectList(
            Wrappers.<MeterGroupDO>lambdaQuery()
                .eq(MeterGroupDO::getGroupId, groupId)
        );

        if (CollUtil.isEmpty(meterGroups)) {
            return new ArrayList<>();
        }

        return meterGroups.stream()
            .map(MeterGroupDO::getMeterId)
            .collect(Collectors.toList());
    }

    /**
     * 批量保存表具与分组关联
     *
     * @param groupId 分组ID
     * @param meterIds 表具ID列表
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveBatch(String groupId, List<String> meterIds) {
        if (CollUtil.isEmpty(meterIds)) {
            return false;
        }

        // 先删除原有关联
        deleteByGroupId(groupId);

        // 批量插入新的关联
        List<MeterGroupDO> meterGroupList = new ArrayList<>();
        for (String meterId : meterIds) {
            MeterGroupDO meterGroupDO = new MeterGroupDO();
            meterGroupDO.setGroupId(groupId);
            meterGroupDO.setMeterId(meterId);
            meterGroupList.add(meterGroupDO);
        }

        for (MeterGroupDO meterGroupDO : meterGroupList) {
            meterGroupMapper.insert(meterGroupDO);
        }

        return true;
    }

    /**
     * 删除表具与分组关联
     *
     * @param groupId 分组ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByGroupId(String groupId) {
        meterGroupMapper.delete(
            Wrappers.<MeterGroupDO>lambdaQuery()
                .eq(MeterGroupDO::getGroupId, groupId)
        );
        return true;
    }
}
