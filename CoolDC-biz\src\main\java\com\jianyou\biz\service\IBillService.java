package com.jianyou.biz.service;

import com.jianyou.biz.domain.MeterDO;
import com.jianyou.biz.domain.bo.BillBO;
import com.jianyou.biz.domain.bo.payBO;
import com.jianyou.biz.domain.vo.BillVO;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;

import java.util.List;

/**
 * 账单Service接口
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IBillService {


    /**
     * 查询账单列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @param isPage  是否分页
     * @return {@link TableDataInfo }<{@link BillVO }>
     */
    TableDataInfo<BillVO> queryPageList(BillBO bo, PageQuery pageQuery, boolean isPage);

    /**
     * 查询账单
     *
     * @param id id
     * @return {@link BillVO }
     */
     BillVO queryById(String id);

    /**
     * 缴费
     */
    Boolean pay(payBO bo);

    /**
     * 生成账单
     *
     * @param meters      表具
     * @param readingWay 抄表方式
     */
    void generateBill(List<MeterDO> meters, String readingWay);
}
