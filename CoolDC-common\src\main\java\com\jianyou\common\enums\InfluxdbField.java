package com.jianyou.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * influxdb测量
 *
 * <AUTHOR>
 * @date 2024/11/14
 */
@Getter
@AllArgsConstructor
public enum InfluxdbField {

    /** 读数 */
    READING("reading");

    private final String name;

    public static InfluxdbField fromName(String name) {
        for (InfluxdbField field : values()) {
            if (field.getName().equals(name)) {
                return field;
            }
        }
        throw new IllegalArgumentException("无效的点位: " + name);
    }
}
