package com.jianyou.biz.service;

import com.jianyou.biz.domain.vo.ProjectVO;
import com.jianyou.biz.domain.bo.ProjectBO;
import com.jianyou.common.core.domain.BaseOptionsVO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 项目信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IProjectService {


    /**
     * 查询项目信息列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link ProjectVO }>
     */
    TableDataInfo<ProjectVO> queryPageList(ProjectBO bo, PageQuery pageQuery);

    /**
     * 查询项目信息列表All
     */
    List<ProjectVO> listAll(ProjectBO bo);

    /**
     * 查询项目信息
     *
     * @param id id
     * @return {@link ProjectVO }
     */
     ProjectVO queryById(String id);

    /**
     * 新增项目信息
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(ProjectBO bo);

    /**
     * 修改项目信息
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(ProjectBO bo);

    /**
     * 校验并批量删除项目信息信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 项目选项
     */
    List<BaseOptionsVO> option(ProjectBO bo);
}
