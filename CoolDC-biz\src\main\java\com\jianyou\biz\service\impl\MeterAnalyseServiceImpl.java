package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjUtil;
import com.jianyou.biz.domain.MeterDO;
import com.jianyou.biz.domain.bo.EnergySummaryBO;
import com.jianyou.biz.domain.bo.MeterAnalyseBO;
import com.jianyou.biz.domain.vo.EnergyAnalyseVO;
import com.jianyou.biz.mapper.MeterMapper;
import com.jianyou.biz.service.IMeterAnalyseService;
import com.jianyou.biz.utils.EnergyAnalyseUtils;
import com.jianyou.common.enums.AnalysisType;
import com.jianyou.common.enums.InfluxdbField;
import com.jianyou.common.enums.InfluxdbMeasurement;
import com.jianyou.common.utils.TimeFormatUtil;
import com.jianyou.common.utils.influxdb.InfluxEnergyAnalyzer;
import com.jianyou.common.utils.influxdb.InfluxUtils;
import com.jianyou.common.utils.influxdb.domain.dto.InfluxRes;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 仪表分析服务实施
 *
 * <AUTHOR>
 * @date 2025/07/02
 */
@Service
@RequiredArgsConstructor
public class MeterAnalyseServiceImpl implements IMeterAnalyseService {

    private final MeterMapper meterMapper;
    private final InfluxUtils influxUtils;

    /**
     * 能耗分析
     */
    @Override
    public EnergyAnalyseVO energyAnalyse(MeterAnalyseBO bo) {
        EnergyAnalyseVO energyAnalyseVO = new EnergyAnalyseVO();
        energyAnalyseVO.init();

        MeterDO meterDO = meterMapper.selectById(bo.getMeterId());
        if (ObjUtil.isEmpty(meterDO)) {
            return energyAnalyseVO;
        }

        AnalysisType analysisType = AnalysisType.getNameByValue(bo.getAnalysisType());
        String startTime = bo.getStartTime();
        String endTime = bo.getEndTime();
        InfluxdbField influxdbField = InfluxdbField.fromName(bo.getPointType());

        // 起始时间向前偏移一个时间单位,这样可以获取能耗和环比
        String qoqStartTime = TimeFormatUtil.offsetTime(startTime, analysisType, -1);
        Date[] qoqRange = TimeFormatUtil.formatRange(qoqStartTime, endTime, analysisType,false);
        List<InfluxRes> resList = influxUtils.selectOne(InfluxdbMeasurement.METER,
            meterDO.getNumber(),
            Collections.singletonList(influxdbField),
            qoqRange[0],
            qoqRange[1],
            true, -3);
        if (CollUtil.isEmpty(resList)) {
            return energyAnalyseVO;
        }
        Map<String, Object> chartsData = InfluxEnergyAnalyzer.energyAnalyze(resList, analysisType, qoqRange[0], qoqRange[1], true);

        // 构建当前能耗和环比能耗 时间
        EnergyAnalyseUtils.buildTimeEnergy(chartsData, energyAnalyseVO);

        // 起始时间和截止时间都减少一年,可以获取同比
        String yoyStartTime = TimeFormatUtil.offsetTime(startTime, AnalysisType.YEARLY, -1);
        String yoyEndTime = TimeFormatUtil.offsetTime(endTime, AnalysisType.YEARLY, -1);
        Date[] yoyRange = TimeFormatUtil.formatRange(yoyStartTime, yoyEndTime, analysisType,false);
        List<InfluxRes> yoyResList = influxUtils.selectOne(InfluxdbMeasurement.METER,
            meterDO.getNumber(),
            Collections.singletonList(influxdbField),
            yoyRange[0],
            yoyRange[1],
            true, -3);
        Map<String, Object> yoyChartsData = InfluxEnergyAnalyzer.energyAnalyze(yoyResList, analysisType, yoyRange[0], yoyRange[1], true);
        LinkedList<Double> yoyEnergy = new LinkedList<>();
        yoyChartsData.forEach((k, v) -> {
            yoyEnergy.add(Convert.toDouble(v));
        });
        energyAnalyseVO.setYoyEnergy(yoyEnergy);
        return energyAnalyseVO;
    }

    /**
     * 能耗总汇
     */
    @Override
    public Double energySummary(EnergySummaryBO bo) {
        MeterDO meterDO = meterMapper.selectById(bo.getMeterId());
        if (ObjUtil.isEmpty(meterDO)) {
            return null;
        }
        InfluxdbField influxdbField = InfluxdbField.fromName(bo.getPointType());
        AnalysisType analysisType = AnalysisType.getNameByValue(bo.getAnalysisType());
        String time = bo.getTime();
        Date[] range = TimeFormatUtil.formatRange(time, time, analysisType,true);
        List<InfluxRes> endList = influxUtils.selectLatest(InfluxdbMeasurement.METER,
            meterDO.getNumber(),
            Collections.singletonList(influxdbField),
            range[0],
            range[1]);
        if (CollUtil.isEmpty(endList)) {
            return null;
        }
        BigDecimal endValue = new BigDecimal(endList.get(0).getValue());
        // 获取上一个单位的时间
        List<InfluxRes> startList = influxUtils.selectLatest(InfluxdbMeasurement.METER,
            meterDO.getNumber(),
            Collections.singletonList(influxdbField),
            null,
            range[0]);

        if (CollUtil.isNotEmpty(startList)) {
            String value = startList.get(0).getValue();
            return endValue.subtract(new BigDecimal(value)).doubleValue();
        } else {
            return endValue.doubleValue();
        }
    }
}
