package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.jianyou.common.core.domain.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 施工进度对象 biz_build_schedule
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_build_schedule")
public class BuildScheduleDO extends BaseDO {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private String id;
    /**
     * 施工计划id
     */
    private String buildPlanId;
    /**
     * 今日工作
     */
    private String todayWork;
    /**
     * 计划工作
     */
    private String planWork;
    /**
     * 现场照片
     */
    private String ossIds;
    /**
     * 项目id
     */
    private String projectId;
    /**
     * 上报时间
     */
    private Date reportTime;

}
