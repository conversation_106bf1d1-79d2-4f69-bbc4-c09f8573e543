package com.jianyou.biz.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.BuildPlanDO;
import com.jianyou.biz.domain.vo.BuildPlanVO;
import com.jianyou.common.core.mapper.BaseMapperPlus;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 施工计划Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface BuildPlanMapper extends BaseMapperPlus<BuildPlanMapper, BuildPlanDO, BuildPlanVO> {

    default Map<String, BuildPlanDO> nameDict(List<String> ids) {
        List<BuildPlanDO> list = selectList(Wrappers.<BuildPlanDO>lambdaQuery()
            .in(CollUtil.isNotEmpty(ids), BuildPlanDO::getId, ids)
        );
        return list.stream().collect(Collectors.toMap(BuildPlanDO::getId, buildPlanDO -> buildPlanDO));
    }
}
