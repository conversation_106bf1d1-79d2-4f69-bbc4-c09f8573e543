package com.jianyou.web.controller.biz;

import java.util.ArrayList;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.utils.poi.ExcelUtil;
import com.jianyou.biz.domain.vo.PlanChangeVO;
import com.jianyou.biz.domain.bo.PlanChangeBO;
import com.jianyou.biz.service.IPlanChangeService;
import com.jianyou.common.core.page.TableDataInfo;

/**
 * 计划变更
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/planChange")
public class PlanChangeController extends BaseController {

    private final IPlanChangeService planChangeService;

    /**
     * 查询计划变更列表
     */
    @SaCheckPermission("biz:planChange:list")
    @GetMapping("/list")
    public TableDataInfo<PlanChangeVO> list(PlanChangeBO bo, PageQuery pageQuery) {
        return planChangeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出计划变更列表
     */
    @SaCheckPermission("biz:planChange:export")
    @PostMapping("/export")
    public void export(PlanChangeBO bo, HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "计划变更", PlanChangeVO.class, response);
    }

    /**
     * 获取计划变更详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:planChange:query")
    @GetMapping("/{id}")
    public R<PlanChangeVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(planChangeService.queryById(id));
    }

    /**
     * 新增计划变更
     */
    @SaCheckPermission("biz:planChange:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PlanChangeBO bo) {
        return toAjax(planChangeService.insertByBo(bo));
    }

    /**
     * 修改计划变更
     */
    @SaCheckPermission("biz:planChange:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PlanChangeBO bo) {
        return toAjax(planChangeService.updateByBo(bo));
    }

    /**
     * 删除计划变更
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:planChange:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(planChangeService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
