package com.jianyou.common.utils.influxdb.domain.pojo;

import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * influxdb表具对象
 *
 * <AUTHOR>
 * @date 2024/11/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Measurement(name = "meter")
public class MeterPojo extends BasePojo {

    /** 表具读数 建议保留两位小数 */
    @Column
    private Double reading;

}
