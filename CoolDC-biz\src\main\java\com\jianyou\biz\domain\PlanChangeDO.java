package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.jianyou.common.core.domain.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 计划变更对象 biz_plan_change
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_plan_change")
public class PlanChangeDO extends BaseDO {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private String id;
    /**
     * 施工计划id
     */
    private String buildPlanId;
    /**
     * 变更事由
     */
    private String changeReason;
    /**
     * 变更单（图片）
     */
    private String ossIds;
    /**
     * 变更状态（未确认、确认、否认、废除）
     */
    private String changeStatus;
    /**
     * 变更时间
     */
    private Date changeTime;
    /**
     * 项目id
     */
    private String projectId;

}
