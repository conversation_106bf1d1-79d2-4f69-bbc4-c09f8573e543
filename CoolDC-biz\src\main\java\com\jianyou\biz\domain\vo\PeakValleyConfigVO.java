package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 分段配置视图对象 enms_ladder_config
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@ExcelIgnoreUnannotated
public class PeakValleyConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 计费配置id
     */
    @ExcelProperty(value = "计费配置id")
    private String billingConfigId;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private String endTime;

    /**
     * 类型(0平;1谷;2峰)
     */
    @ExcelProperty(value = "类型(0平;1谷;2峰)")
    private String type;

    /**
     * 单价
     */
    @ExcelProperty(value = "单价")
    private BigDecimal price;


}
