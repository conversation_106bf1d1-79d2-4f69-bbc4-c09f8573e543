package com.jianyou.biz.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 费用明细
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChargeDetail {
    // 阶段名：标准/第一档/第二档
    private String phase;
    // 峰/平/谷
    private String timeType;
    // 实际时间范围，如"08:00–10:00"
    private String timeRange;
    // 电量
    private BigDecimal volume;
    // 基价
    private BigDecimal basePrice;
    // 加价（峰/谷加价）
    private BigDecimal addPrice;
    // 单价（基价 + 加价）
    private BigDecimal unitPrice;
    // 费用
    private BigDecimal fee;
    // 阶梯上限
    private BigDecimal maxVolume;

    public ChargeDetail(String phase, String timeType, String timeRange,
                        BigDecimal volume, BigDecimal basePrice,
                        BigDecimal addPrice, BigDecimal fee,
                        BigDecimal maxVolume) {
        this.phase = phase;
        this.timeType = timeType;
        this.timeRange = timeRange;
        this.volume = volume;
        this.basePrice = basePrice;
        this.addPrice = addPrice;
        this.unitPrice = basePrice.add(addPrice);
        this.fee = fee;
        this.maxVolume = maxVolume;
    }
}
