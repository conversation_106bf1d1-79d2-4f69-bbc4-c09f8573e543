package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 费用 bo
 *
 * <AUTHOR>
 * @date 2025/07/16
 */
@Data
public class FeeBO {

    @NotBlank(message = "表具ID不能为空", groups = {EditGroup.class, AddGroup.class})
    private String meterId;
    @NotNull(message = "金额不能为空", groups = {AddGroup.class})
    private BigDecimal amount;
    @NotNull(message = "是否自动扣费不能为空", groups = {EditGroup.class})
    private String autoPayFlag;
}
