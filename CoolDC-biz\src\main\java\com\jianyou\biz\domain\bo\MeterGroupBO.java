package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;


/**
 * 表具分组关联业务对象 enms_meter_group
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MeterGroupBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String id;

    /**
     * 分组id
     */
    @NotBlank(message = "分组id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String groupId;

    /**
     * 表具id
     */
    @NotBlank(message = "表具id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String meterId;


}
