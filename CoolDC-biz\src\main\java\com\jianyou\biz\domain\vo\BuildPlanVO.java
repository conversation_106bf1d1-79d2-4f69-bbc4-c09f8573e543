package com.jianyou.biz.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 施工计划视图对象 biz_build_plan
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
public class BuildPlanVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 项目ID
     */
    private String projectId;
    @ExcelProperty(value = "项目")
    private String projectName;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String details;

    /**
     * 分项负责人
     */
    @ExcelProperty(value = "分项负责人")
    private String principal;

    /**
     * 分项负责人电话
     */
    @ExcelProperty(value = "分项负责人电话")
    private String principalNumber;

    /**
     * 施工开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "施工开始时间")
    private Date startTime;

    /**
     * 施工截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "施工截止时间")
    private Date endTime;

    /**
     * 变更状态（未确认、确认、否认、废除）
     */
    private String changeStatus;


}
