package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jianyou.common.core.domain.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 表具对象 enms_meter
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("enms_meter")
public class MeterDO extends BaseDO {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private String id;
    /**
     * 名称
     */
    private String name;
    /**
     * 表具编号
     */
    private String number;
    /**
     * 房号
     */
    private String roomNumber;
    /**
     * 区域Id
     */
    private String areaId;
    /**
     * 网关id
     */
    private String gatewayId;
    /**
     * 能源类型
     */
    private String energyType;
    /**
     * 表具状态
     */
    private String meterStatus;
    /**
     * 在线状态
     */
    private String onlineStatus;
    /**
     * 表具读数
     */
    private BigDecimal meterRecord;
    /**
     * 表具时间
     */
    private String meterTime;
    /**
     * 同步时间
     */
    private Date syncTime;
    /**
     * 是否重点表具;1是0否
     */
    private String emphasisFlag;

    /**
     * 账户余额
     */
    private BigDecimal balance;

    /**
     * 是否自动扣费;1是0否
     */
    private String autoPayFlag;

}
