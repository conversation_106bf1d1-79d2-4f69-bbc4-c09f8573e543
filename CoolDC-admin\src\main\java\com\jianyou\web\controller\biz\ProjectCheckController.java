package com.jianyou.web.controller.biz;

import java.util.ArrayList;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.utils.poi.ExcelUtil;
import com.jianyou.biz.domain.vo.ProjectCheckVO;
import com.jianyou.biz.domain.bo.ProjectCheckBO;
import com.jianyou.biz.service.IProjectCheckService;
import com.jianyou.common.core.page.TableDataInfo;

/**
 * 项目验收
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/projectCheck")
public class ProjectCheckController extends BaseController {

    private final IProjectCheckService projectCheckService;

    /**
     * 查询项目验收列表
     */
    @SaCheckPermission("biz:projectCheck:list")
    @GetMapping("/list")
    public TableDataInfo<ProjectCheckVO> list(ProjectCheckBO bo, PageQuery pageQuery) {
        return projectCheckService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目验收列表
     */
    @SaCheckPermission("biz:projectCheck:export")
    @PostMapping("/export")
    public void export(ProjectCheckBO bo, HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "项目验收", ProjectCheckVO.class, response);
    }

    /**
     * 获取项目验收详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:projectCheck:query")
    @GetMapping("/{id}")
    public R<ProjectCheckVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(projectCheckService.queryById(id));
    }

    /**
     * 新增项目验收
     */
    @SaCheckPermission("biz:projectCheck:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProjectCheckBO bo) {
        return toAjax(projectCheckService.insertByBo(bo));
    }

    /**
     * 修改项目验收
     */
    @SaCheckPermission("biz:projectCheck:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProjectCheckBO bo) {
        return toAjax(projectCheckService.updateByBo(bo));
    }

    /**
     * 删除项目验收
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:projectCheck:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(projectCheckService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
