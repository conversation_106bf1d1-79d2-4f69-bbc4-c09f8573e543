package com.jianyou.biz.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.MeterAccountLogDO;
import com.jianyou.biz.domain.bo.MeterAccountLogBO;
import com.jianyou.biz.domain.vo.MeterAccountLogVO;
import com.jianyou.biz.mapper.MeterAccountLogMapper;
import com.jianyou.biz.service.IMeterAccountLogService;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预存账户流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@RequiredArgsConstructor
@Service
public class MeterAccountLogServiceImpl implements IMeterAccountLogService {

    private final MeterAccountLogMapper baseMapper;

    /**
     * 查询预存账户流水列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link MeterAccountLogVO }>
     */
    @Override
    public TableDataInfo<MeterAccountLogVO> queryPageList(MeterAccountLogBO bo, PageQuery pageQuery) {
        IPage<MeterAccountLogVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<MeterAccountLogDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getMeterId()), MeterAccountLogDO::getMeterId, bo.getMeterId())
            .between(ObjUtil.isNotEmpty(bo.getStartCreatedTime()) && ObjUtil.isNotEmpty(bo.getEndCreatedTime()),
                MeterAccountLogDO::getCreatedTime, bo.getStartCreatedTime(), bo.getEndCreatedTime())
            .orderByDesc(MeterAccountLogDO::getCreatedTime)
        );
        return TableDataInfo.build(page);
    }

    /**
     * 记录表具账户变动流水
     *
     * @param meterId       仪表id
     * @param beforeBalance 原本余额
     * @param changeAmount  变动金额
     * @param type          类型：1=充值，2=扣费，3=退费，4=手工调整
     * @param remark        备注
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Boolean recordAccountChange(String meterId, BigDecimal beforeBalance, BigDecimal changeAmount, String type, String remark) {
        // 根据类型自动调整变动金额的正负值
        BigDecimal actualChangeAmount = getActualChangeAmount(changeAmount, type);

        // 计算变动后余额
        BigDecimal afterBalance = beforeBalance.add(actualChangeAmount);

        // 创建流水记录
        MeterAccountLogDO log = new MeterAccountLogDO();
        log.setMeterId(meterId);
        log.setChangeAmount(actualChangeAmount);
        log.setType(type);
        log.setBeforeBalance(beforeBalance);
        log.setAfterBalance(afterBalance);
        log.setCreatedTime(new Date());
        log.setRemark(remark);

        return baseMapper.insert(log) > 0;
    }

    /**
     * 获取实际更改金额
     *
     * @param changeAmount 变动金额
     * @param type         类型
     * @return {@link BigDecimal }
     */
    private static BigDecimal getActualChangeAmount(BigDecimal changeAmount, String type) {
        BigDecimal actualChangeAmount = changeAmount;

        // 当类型为扣费(2)或退费(3)时，确保金额为负数
        if ("2".equals(type) || "3".equals(type)) {
            // 如果传入的是正数，则转为负数
            if (changeAmount.compareTo(BigDecimal.ZERO) > 0) {
                actualChangeAmount = changeAmount.negate();
            }
        }
        // 当类型为充值(1)时，确保金额为正数
        else if ("1".equals(type)) {
            // 如果传入的是负数，则转为正数
            if (changeAmount.compareTo(BigDecimal.ZERO) < 0) {
                actualChangeAmount = changeAmount.abs();
            }
        }
        // 类型为手工调整(4)时，可以是正数也可以是负数，保持原值
        return actualChangeAmount;
    }
}
