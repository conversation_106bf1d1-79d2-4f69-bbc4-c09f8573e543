package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 分段配置对象 enms_peak_valley_config
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("enms_peak_valley_config")
public class PeakValleyConfigDO {

    /**
     * ID
     */
    private String id;
    /**
     * 计费配置id
     */
    private String billingConfigId;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 类型(0平;1谷;2峰)
     */
    private String type;
    /**
     * 单价
     */
    private BigDecimal price;

}
