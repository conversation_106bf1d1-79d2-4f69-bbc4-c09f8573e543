package com.jianyou.common.utils.influxdb;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.QueryApi;
import com.influxdb.client.WriteApiBlocking;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import com.jianyou.common.enums.InfluxdbField;
import com.jianyou.common.enums.InfluxdbMeasurement;
import com.jianyou.common.exception.ServiceException;
import com.jianyou.common.utils.influxdb.domain.dto.InfluxRes;
import com.jianyou.common.utils.influxdb.domain.dto.WindowAggregationResult;
import com.jianyou.common.utils.influxdb.domain.pojo.BasePojo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 时序库查询工具
 *
 * <AUTHOR>
 * @date 2025/07/02
 */
@Component
@RequiredArgsConstructor
public class InfluxUtils {

    @Value("${influxdb.bucket}")
    private String bucket;
    /** 标签(唯一标识)名 */
    private final static String TAG_NAME = "tagId";
    private final InfluxDBClient influxDBClient;

    /**
     * 查询指定时间范围中最新一条数据
     *
     * @param measurement 测量(需查询的表)
     * @param tag         标签(设备id或唯一标识)
     * @param fields      字段
     * @param startTime   起始时间 为空则默认为截止时间6个月前
     * @param endTime     截止时间
     * @return {@link List}<{@link InfluxRes}> 如果查询了多个field,则需要根据field进行分组
     */
    public List<InfluxRes> selectLatest(@NotNull InfluxdbMeasurement measurement, String tag, List<InfluxdbField> fields, Date startTime, @NotNull Date endTime) {
        Date lastTime;
        if (ObjUtil.isNotEmpty(startTime)){
            lastTime = startTime;
        }else {
            lastTime = DateUtil.offsetMonth(endTime,-6);
        }
        // 构建查询语句，查询指定时间范围内且按时间倒序排序的数据，并限制只返回各标签字段的一条记录
        String line = buildLine(measurement, Collections.singletonList(tag), fields, lastTime, endTime, true, 1);
        // 执行查询并返回结果
        return query(line);
    }

    /**
     * 批量查询指定时间范围中最新一条数据
     *
     * @param measurement 测量(需查询的表)
     * @param tags        标签(设备id或唯一标识)
     * @param fields      字段
     * @param startTime   起始时间 为空则默认为截止时间6个月前
     * @param endTime     截止时间
     * @return {@link Map }<{@link String }, {@link List }<{@link InfluxRes }>> k:标签 v:数据 如果查询了多个field,则需要根据field进行分组
     */
    public Map<String, List<InfluxRes>> selectBatchLatest(@NotNull InfluxdbMeasurement measurement, List<String> tags, List<InfluxdbField> fields, Date startTime, @NotNull Date endTime) {
        Date lastTime;
        if (ObjUtil.isNotEmpty(startTime)){
            lastTime = startTime;
        }else {
            lastTime = DateUtil.offsetMonth(endTime,-6);
        }
        // 构建查询语句，查询指定时间范围内且按时间倒序排序的数据，并限制只返回各标签字段的一条记录
        String line = buildLine(measurement, tags, fields, lastTime, endTime, true, 1);
        // 执行查询并返回结果
        return query(line).stream().collect(Collectors.groupingBy(InfluxRes::getTag));
    }

    /**
     * 根据单个tag查询
     *
     * @param measurement     测量(需查询的表)
     * @param tag             标签(设备id或唯一标识)
     * @param fields          字段
     * @param startTime       开始时间 注:会自动偏移天数,为数据分析需求
     * @param endTime         结束时间
     * @param orderByTime     true:倒序 false:正序
     * @param startOffsetDays 起始时间偏移天数
     * @return {@link List}<{@link InfluxRes}> 如果查询了多个field,则需要根据field进行分组
     */
    public List<InfluxRes> selectOne(@NotNull InfluxdbMeasurement measurement, String tag, List<InfluxdbField> fields,
                                     @NotNull Date startTime, @NotNull Date endTime, boolean orderByTime, int startOffsetDays) {
        // 自动偏移天数,为数据分析需求
        DateTime offsetStartTime = DateUtil.offset(startTime, DateField.DAY_OF_YEAR, startOffsetDays);
        String line = buildLine(measurement, Collections.singletonList(tag), fields, offsetStartTime, endTime, orderByTime);
        List<InfluxRes> tagOne = query(line).stream().collect(Collectors.groupingBy(InfluxRes::getTag)).get(tag);
        return tagOne == null ? new ArrayList<>() : tagOne;
    }

    /**
     * 根据多个tag查询
     *
     * @param measurement     测量(需查询的表)
     * @param tags            标签(设备id或唯一标识)
     * @param fields          字段
     * @param startTime       开始时间 注:会自动偏移天数,为数据分析需求
     * @param endTime         结束时间
     * @param orderByTime     true:倒序 false:正序
     * @param startOffsetDays 起始时间偏移天数
     * @return {@link Map }<{@link String },{@link List }<{@link InfluxRes }>> k:标签 v:数据 如果查询了多个field,则需要根据field进行分组
     */
    public Map<String, List<InfluxRes>> selectBatch(@NotNull InfluxdbMeasurement measurement, List<String> tags, List<InfluxdbField> fields,
                                                    @NotNull Date startTime, @NotNull Date endTime, boolean orderByTime, int startOffsetDays) {
        // 自动偏移天数,为数据分析需求
        DateTime offsetStartTime = DateUtil.offset(startTime, DateField.DAY_OF_YEAR, startOffsetDays);
        String line = buildLine(measurement, tags, fields, offsetStartTime, endTime, orderByTime);
        return query(line).stream().collect(Collectors.groupingBy(InfluxRes::getTag));
    }

    /**
     * 插入数据
     */
    public <T extends BasePojo> void insertPojo(@NotNull List<T> pojoList) {
        if (CollUtil.isEmpty(pojoList)) {
            return;
        }
        WriteApiBlocking writeApiBlocking = influxDBClient.getWriteApiBlocking();
        writeApiBlocking.writeMeasurements(WritePrecision.S, pojoList);
    }

    /**
     * 时间开窗聚合查询 - 支持多维度开窗并根据指定字段进行sum聚合
     * 时区设置为上海时区（UTC+8），从0点开始计算窗口
     *
     * @param measurement    测量(需查询的表)
     * @param tags           标签(设备id或唯一标识)列表
     * @param field          需要聚合的字段
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param windowDuration 时间窗口大小，如："1h"(1小时), "1d"(1天), "15m"(15分钟)
     * @param groupByTags    分组维度标签列表，可为空
     * @return {@link List}<{@link WindowAggregationResult}> 聚合结果列表
     */
    public List<WindowAggregationResult> selectWindowAggregation(@NotNull InfluxdbMeasurement measurement,
                                                                 List<String> tags,
                                                                 @NotNull InfluxdbField field,
                                                                 @NotNull Date startTime,
                                                                 @NotNull Date endTime,
                                                                 @NotNull String windowDuration,
                                                                 List<String> groupByTags) {
        // 构建时间开窗聚合查询语句
        String fluxQuery = buildWindowAggregationQuery(measurement, tags, field, startTime, endTime, windowDuration, groupByTags);

        // 执行查询并返回聚合结果
        return queryWindowAggregation(fluxQuery);
    }

    /**
     * 查询数据
     *
     * @param line 行协议
     * @return {@link List }<{@link InfluxRes }>
     */
    private List<InfluxRes> query(String line) {
        QueryApi queryApi = influxDBClient.getQueryApi();
        List<FluxTable> fluxTables = queryApi.query(line);
        List<InfluxRes> list = new ArrayList<>();
        // 遍历每个 FluxTable
        fluxTables.forEach(fluxTable -> {
            // 遍历每个 FluxRecord
            List<FluxRecord> records = fluxTable.getRecords();
            records.forEach(record -> {
                // 获取tag字段值
                String tag = (String) record.getValueByKey(TAG_NAME);
                // 获取字段名
                String field = record.getField();
                // 获取字段的实际值
                Object value = record.getValue();
                // 获取记录的时间戳
                Instant time = record.getTime();
                // 构建对象
                InfluxRes influxRes = new InfluxRes();
                influxRes.setTag(tag);
                influxRes.setField(field);
                influxRes.setTime(time != null ? DateUtil.formatDateTime(Date.from(time)) : "");
                influxRes.setValue(Convert.toStr(value));
                list.add(influxRes);
            });
        });
        return list;
    }

    /**
     * 构建查询语句
     *
     * @param measurement 测量
     * @param tags        标签值
     * @param fields      字段
     * @param startTime   开始时间 时间为空则默认近6个月查一条
     * @param endTime     结束时间
     * @param orderByTime true:倒序 false:正序
     * @return {@link String }
     */
    private String buildLine(InfluxdbMeasurement measurement, List<String> tags, List<InfluxdbField> fields, Date startTime, Date endTime, boolean orderByTime) {
        return buildLine(measurement, tags, fields, startTime, endTime, orderByTime, null);
    }

    /**
     * 构建查询语句
     *
     * @param measurement 测量
     * @param tags        标签值
     * @param fields      字段
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param orderByTime true:倒序 false:正序
     * @param limitNum    限制返回的记录数，可为空
     * @return {@link String }
     */
    private String buildLine(@NotNull InfluxdbMeasurement measurement,
                             List<String> tags,
                             List<InfluxdbField> fields,
                             @NotNull Date startTime,
                             @NotNull Date endTime,
                             boolean orderByTime,
                             Integer limitNum) {

        if (ObjUtil.isEmpty(measurement)) {
            throw new ServiceException("测量不能为空");
        }
        if (ObjUtil.isEmpty(startTime) || ObjUtil.isEmpty(endTime)) {
            throw new ServiceException("时间范围不能为空");
        }

        // InfluxDB stop 时间是开区，补 1 秒
        Instant oneSecondLater = endTime.toInstant().plusSeconds(1);

        StringBuilder filterBuilder = new StringBuilder();

        // 添加测量过滤
        filterBuilder.append("r[\"_measurement\"] == \"").append(measurement.getMeasurementName()).append("\"");

        // 添加 tag 过滤
        if (CollUtil.isNotEmpty(tags)) {
            filterBuilder.append(" and ").append(buildTag(tags));
        }

        // 添加 field 过滤
        if (CollUtil.isNotEmpty(fields)) {
            filterBuilder.append(" and ").append(buildField(fields));
        }

        // 开始拼接完整 Flux 查询
        StringBuilder flux = new StringBuilder();
        flux.append("from(bucket: \"").append(bucket).append("\")\n")
            .append("  |> range(start: ").append(startTime.toInstant()).append(", stop: ").append(oneSecondLater).append(")\n")
            .append("  |> filter(fn: (r) => ").append(filterBuilder).append(")\n")
            .append("  |> sort(columns: [\"_time\"], desc: ").append(orderByTime).append(")");

        if (limitNum != null) {
            flux.append("\n  |> limit(n: ").append(limitNum).append(")");
        }

        return flux.toString();
    }


    /**
     * 构建标签
     *
     * @param tags 标签
     * @return {@link StringBuilder }
     */
    private StringBuilder buildTag(@NotNull List<String> tags) {
        if (CollUtil.isEmpty(tags)) {
            throw new ServiceException("标签值不能为空");
        }
        StringBuilder tagFlux = new StringBuilder();
        tagFlux.append("contains(value: r[\"").append(TAG_NAME).append("\"], set: [");

        for (int i = 0; i < tags.size(); i++) {
            tagFlux.append("\"").append(tags.get(i)).append("\"");
            if (i != tags.size() - 1) {
                tagFlux.append(", ");
            }
        }
        tagFlux.append("])");
        return tagFlux;
    }

    /**
     * 构建字段
     *
     * @param fields 字段
     * @return {@link StringBuilder }
     */
    private StringBuilder buildField(@NotNull List<InfluxdbField> fields) {
        if (CollUtil.isEmpty(fields)) {
            throw new ServiceException("字段不能为空");
        }
        StringBuilder fieldFlux = new StringBuilder();
        fieldFlux.append("contains(value: r[\"_field\"], set: [");
        for (int i = 0; i < fields.size(); i++) {
            fieldFlux.append("\"").append(fields.get(i).getName()).append("\"");
            if (i != fields.size() - 1) {
                fieldFlux.append(", ");
            }
        }
        fieldFlux.append("])");
        return fieldFlux;
    }

    /**
     * 构建时间开窗聚合查询语句
     *
     * @param measurement    测量
     * @param tags           标签值列表
     * @param field          需要聚合的字段
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param windowDuration 时间窗口大小
     * @param groupByTags    分组维度标签列表
     * @return Flux查询语句
     */
    private String buildWindowAggregationQuery(@NotNull InfluxdbMeasurement measurement,
                                               List<String> tags,
                                               @NotNull InfluxdbField field,
                                               @NotNull Date startTime,
                                               @NotNull Date endTime,
                                               @NotNull String windowDuration,
                                               List<String> groupByTags) {

        if (ObjUtil.isEmpty(measurement)) {
            throw new ServiceException("测量不能为空");
        }
        if (ObjUtil.isEmpty(startTime) || ObjUtil.isEmpty(endTime)) {
            throw new ServiceException("时间范围不能为空");
        }
        if (ObjUtil.isEmpty(windowDuration)) {
            throw new ServiceException("时间窗口大小不能为空");
        }

        // InfluxDB stop 时间是开区间，补 1 秒
        Instant oneSecondLater = endTime.toInstant().plusSeconds(1);

        StringBuilder flux = new StringBuilder();

        // 基础查询
        flux.append("from(bucket: \"").append(bucket).append("\")\n")
            .append("  |> range(start: ").append(startTime.toInstant()).append(", stop: ").append(oneSecondLater).append(")\n")
            .append("  |> filter(fn: (r) => r[\"_measurement\"] == \"").append(measurement.getMeasurementName()).append("\")\n")
            .append("  |> filter(fn: (r) => r[\"_field\"] == \"").append(field.getName()).append("\")\n");

        // 添加标签过滤
        if (CollUtil.isNotEmpty(tags)) {
            flux.append("  |> filter(fn: (r) => ").append(buildTag(tags)).append(")\n");
        }

        // 构建分组列
        StringBuilder groupColumns = new StringBuilder();
        groupColumns.append("[\"_measurement\", \"_field\", \"").append(TAG_NAME).append("\"");

        // 添加额外的分组维度
        if (CollUtil.isNotEmpty(groupByTags)) {
            for (String groupTag : groupByTags) {
                groupColumns.append(", \"").append(groupTag).append("\"");
            }
        }
        groupColumns.append("]");

        // 时间开窗聚合 - 使用上海时区，从0点开始
        flux.append("  |> aggregateWindow(\n")
            .append("    every: ").append(windowDuration).append(",\n")
            .append("    fn: sum,\n")
            .append("    createEmpty: false,\n")
            .append("    timeSrc: \"_start\",\n")
            .append("    offset: 8h\n")  // 上海时区偏移量 UTC+8
            .append("  )\n")
            .append("  |> group(columns: ").append(groupColumns).append(")\n")
            .append("  |> sort(columns: [\"_time\"])");

        return flux.toString();
    }

    /**
     * 执行时间开窗聚合查询
     *
     * @param fluxQuery Flux查询语句
     * @return 聚合结果列表
     */
    private List<WindowAggregationResult> queryWindowAggregation(String fluxQuery) {
        QueryApi queryApi = influxDBClient.getQueryApi();
        List<FluxTable> fluxTables = queryApi.query(fluxQuery);
        List<WindowAggregationResult> results = new ArrayList<>();

        // 遍历每个 FluxTable
        fluxTables.forEach(fluxTable -> {
            // 遍历每个 FluxRecord
            List<FluxRecord> records = fluxTable.getRecords();
            records.forEach(record -> {
                WindowAggregationResult result = new WindowAggregationResult();

                // 获取基本信息
                result.setTag((String) record.getValueByKey(TAG_NAME));
                result.setField(record.getField());

                // 获取聚合值
                Object value = record.getValue();
                if (value instanceof Number) {
                    result.setAggregatedValue(((Number) value).doubleValue());
                } else {
                    result.setAggregatedValue(0.0);
                }

                // 获取时间窗口信息
                Instant windowStart = record.getTime();
                if (windowStart != null) {
                    result.setWindowStart(DateUtil.formatDateTime(Date.from(windowStart)));
                }

                // 获取窗口结束时间（如果存在）
                Object stopTime = record.getValueByKey("_stop");
                if (stopTime instanceof Instant) {
                    result.setWindowStop(DateUtil.formatDateTime(Date.from((Instant) stopTime)));
                }

                // 获取其他维度标签
                Map<String, String> dimensions = new HashMap<>();
                record.getValues().forEach((key, val) -> {
                    // 排除系统字段，只保留用户自定义的标签
                    if (!key.startsWith("_") && !TAG_NAME.equals(key) && val instanceof String) {
                        dimensions.put(key, (String) val);
                    }
                });
                result.setDimensions(dimensions);

                results.add(result);
            });
        });

        return results;
    }

}
