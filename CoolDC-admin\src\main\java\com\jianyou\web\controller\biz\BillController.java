package com.jianyou.web.controller.biz;


import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jianyou.biz.domain.bo.BillBO;
import com.jianyou.biz.domain.bo.payBO;
import com.jianyou.biz.domain.vo.BillVO;
import com.jianyou.biz.service.IBillService;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

/**
 * 账单
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/bill")
public class BillController extends BaseController {

    private final IBillService billService;

    /**
     * 查询账单列表
     */
    @SaCheckPermission("biz:bill:list")
    @GetMapping("/list")
    public TableDataInfo<BillVO> list(BillBO bo, PageQuery pageQuery) {
        return billService.queryPageList(bo, pageQuery, true);
    }

    /**
     * 导出账单列表
     */
    @SaCheckPermission("biz:bill:export")
    @PostMapping("/export")
    public void export(BillBO bo, HttpServletResponse response) {
        TableDataInfo<BillVO> tableDataInfo = billService.queryPageList(bo, null, false);
        ExcelUtil.exportExcel(tableDataInfo.getRows(), "账单", BillVO.class, response);
    }

    /**
     * 获取账单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:bill:query")
    @GetMapping("/{id}")
    public R<BillVO> getInfo(@NotNull(message = "主键不能为空")
                             @PathVariable String id) {
        return R.ok(billService.queryById(id));
    }

    /**
     * 缴费
     */
    @SaCheckPermission("biz:bill:pay")
    @RepeatSubmit()
    @PostMapping("/pay")
    public R<Void> pay(@Validated(EditGroup.class) @RequestBody payBO bo) {
        return toAjax(billService.pay(bo));
    }

}
