package com.jianyou.web.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jianyou.biz.domain.bo.MeterReadingBO;
import com.jianyou.biz.domain.vo.MeterReadingVO;
import com.jianyou.biz.service.IMeterReadingService;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.util.Arrays;

/**
 * 抄表记录
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/meterReading")
public class MeterReadingController extends BaseController {

    private final IMeterReadingService meterReadingService;

    /**
     * 查询抄表记录列表
     */
    @SaCheckPermission("biz:meterReading:list")
    @GetMapping("/list")
    public TableDataInfo<MeterReadingVO> list(MeterReadingBO bo, PageQuery pageQuery) {
        return meterReadingService.queryPageList(bo, pageQuery, true);
    }

    /**
     * 导出抄表记录列表
     */
    @SaCheckPermission("biz:meterReading:export")
    @PostMapping("/export")
    public void export(MeterReadingBO bo, HttpServletResponse response) {
        TableDataInfo<MeterReadingVO> tableDataInfo = meterReadingService.queryPageList(bo, null, false);
        ExcelUtil.exportExcel(tableDataInfo.getRows(), "抄表记录", MeterReadingVO.class, response);
    }

    /**
     * 删除抄表记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:meterReading:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(meterReadingService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
