package com.jianyou.common.enums;

import com.jianyou.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 分析方式枚举
 *
 * <AUTHOR>
 * @date 2025/07/02
 */
@Getter
@AllArgsConstructor
public enum AnalysisType {

    /** 年 */ YEARLY("0", "yyyy", 1),
    /** 周 */ WEEK("1", "MM-dd", 4),
    /** 月 */ MONTHLY("2", "yyyy-MM", 2),
    /** 日 */ DAILY("3", "yyyy-MM-dd", 5),
    /** 时 */ HOURLY("4", "yyyy-MM-dd HH", 11);

    private final String value;
    private final String format;
    private final Integer dateField;

    public static AnalysisType getNameByValue(String value) {
        return Arrays.stream(AnalysisType.values())
            .filter(l -> l.getValue().equals(value))
            .findFirst()
            .orElseThrow(() -> new ServiceException("无效的分析方式: " + value));
    }

}
