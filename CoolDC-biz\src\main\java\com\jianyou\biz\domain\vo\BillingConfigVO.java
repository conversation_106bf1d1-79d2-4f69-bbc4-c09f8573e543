package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 计费配置视图对象 enms_billing_config
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@ExcelIgnoreUnannotated
public class BillingConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 能源类型
     */
    @ExcelProperty(value = "能源类型")
    private String energyType;

    /**
     * 费率类型 0标准 1阶梯
     */
    @ExcelProperty(value = "费率类型")
    private String rateType;

    /**
     * 是否分段(1是0否)
     */
    private String peakValleyFlag;
    /**
     * 标准单价
     */
    private BigDecimal standardPrice;

    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    private String unit;

    /**
     * 起始有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "起始有效期")
    private Date startTime;

    /**
     * 截止有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "截止有效期")
    private Date endTime;

    /** 配置版本 用来检测配置是否更改 */
    private String configVersions;

    /**
     * 分段配置列表
     */
    private List<PeakValleyConfigVO> peakValleyConfigs;

    /**
     * 阶梯配置列表
     */
    private List<TieredConfigVO> tieredConfigs;

}
