package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.BuildPlanDO;
import com.jianyou.biz.domain.PlanChangeDO;
import com.jianyou.biz.mapper.BuildPlanMapper;
import com.jianyou.biz.service.IBuildPlanService;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.jianyou.biz.mapper.PlanChangeMapper;
import com.jianyou.biz.service.IPlanChangeService;
import com.jianyou.biz.domain.vo.PlanChangeVO;
import com.jianyou.biz.domain.bo.PlanChangeBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 计划变更Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RequiredArgsConstructor
@Service
public class PlanChangeServiceImpl implements IPlanChangeService {

    private final PlanChangeMapper baseMapper;
    private final BuildPlanMapper buildPlanMapper;
    private final IBuildPlanService buildPlanService;

    /**
     * 查询计划变更列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link PlanChangeVO }>
     */
    @Override
    public TableDataInfo<PlanChangeVO> queryPageList(PlanChangeBO bo, PageQuery pageQuery) {
        IPage<PlanChangeVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<PlanChangeDO>lambdaQuery()
            .eq(StrUtil.isNotEmpty(bo.getBuildPlanId()), PlanChangeDO::getBuildPlanId, bo.getBuildPlanId())
            .eq(StrUtil.isNotEmpty(bo.getProjectId()), PlanChangeDO::getProjectId, bo.getProjectId())
        );
        List<PlanChangeVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new TableDataInfo<>();
        }
        Map<String, BuildPlanDO> map = buildPlanMapper.nameDict(records.stream().map(PlanChangeVO::getBuildPlanId).collect(Collectors.toList()));
        records.forEach(record -> {
            BuildPlanDO buildPlanDO = map.get(record.getBuildPlanId());
            if (buildPlanDO != null) {
                record.setBuildPlanName(buildPlanDO.getName());
            }
        });
        return TableDataInfo.build(page);
    }

    /**
     * 查询计划变更
     *
     * @param id id
     * @return {@link PlanChangeVO }
     */
    @Override
    public PlanChangeVO queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增计划变更
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(PlanChangeBO bo) {
        PlanChangeDO planChangeDO = new PlanChangeDO();
        BeanCopyUtils.copy(bo, planChangeDO);
        planChangeDO.create();
        planChangeDO.update();
        baseMapper.insert(planChangeDO);
        // 变更计划
        buildPlanService.planChange(bo.getBuildPlanId(), bo.getChangeStatus());
        return true;
    }

    /**
     * 修改计划变更
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(PlanChangeBO bo) {
        PlanChangeDO planChangeDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(planChangeDO)) {
            return false;
        }
        BeanCopyUtils.copy(bo, planChangeDO);
        planChangeDO.update();
        baseMapper.updateById(planChangeDO);
        // 变更计划
        buildPlanService.planChange(bo.getBuildPlanId(), bo.getChangeStatus());
        return true;
    }

    /**
     * 校验并批量删除计划变更信息
     *
     * @param ids     主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
