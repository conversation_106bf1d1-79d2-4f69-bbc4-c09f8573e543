package com.jianyou.web.controller.biz;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.jianyou.common.core.domain.BaseOptionsVO;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.utils.poi.ExcelUtil;
import com.jianyou.biz.domain.vo.BuildPlanVO;
import com.jianyou.biz.domain.bo.BuildPlanBO;
import com.jianyou.biz.service.IBuildPlanService;
import com.jianyou.common.core.page.TableDataInfo;

/**
 * 施工计划
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/buildPlan")
public class BuildPlanController extends BaseController {

    private final IBuildPlanService buildPlanService;

    /**
     * 查询施工计划列表
     */
    @SaCheckPermission("biz:buildPlan:list")
    @GetMapping("/list")
    public TableDataInfo<BuildPlanVO> list(BuildPlanBO bo, PageQuery pageQuery) {
        return buildPlanService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出施工计划列表
     */
    @SaCheckPermission("biz:buildPlan:export")
    @PostMapping("/export")
    public void export(BuildPlanBO bo, HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "施工计划", BuildPlanVO.class, response);
    }

    /**
     * 获取施工计划详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:buildPlan:query")
    @GetMapping("/{id}")
    public R<BuildPlanVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(buildPlanService.queryById(id));
    }

    /**
     * 新增施工计划
     */
    @SaCheckPermission("biz:buildPlan:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BuildPlanBO bo) {
        return toAjax(buildPlanService.insertByBo(bo));
    }

    /**
     * 修改施工计划
     */
    @SaCheckPermission("biz:buildPlan:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BuildPlanBO bo) {
        return toAjax(buildPlanService.updateByBo(bo));
    }

    /**
     * 删除施工计划
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:buildPlan:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(buildPlanService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 施工计划选项
     */
    @GetMapping("/option")
    public R<List<BaseOptionsVO>> option(BuildPlanBO bo) {
        return R.ok(buildPlanService.option(bo));
    }
}
