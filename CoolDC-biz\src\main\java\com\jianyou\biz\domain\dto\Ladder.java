package com.jianyou.biz.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 阶梯
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Ladder {
    // 最大电量
    private BigDecimal maxVolume;
    // 基价
    private BigDecimal price;
    // 阶段名：第一档/第二档等
    private String name;
}
