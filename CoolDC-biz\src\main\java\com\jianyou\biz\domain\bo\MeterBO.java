package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 表具业务对象 enms_meter
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MeterBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 表具编号
     */
    @NotBlank(message = "表具编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String number;

    /**
     * 房号
     */
    @NotBlank(message = "房号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String roomNumber;

    /**
     * 区域Id
     */
    @NotBlank(message = "区域不能为空", groups = { AddGroup.class, EditGroup.class })
    private String areaId;

    /**
     * 网关id
     */
    private String gatewayId;

    /**
     * 能源类型
     */
    @NotBlank(message = "能源类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String energyType;

    /**
     * 表具状态
     */
    private String meterStatus;

    /**
     * 在线状态
     */
    private String onlineStatus;

    /**
     * 是否重点表具;
     */
    @NotBlank(message = "是否重点表具不能为空", groups = { AddGroup.class, EditGroup.class })
    private String emphasisFlag;

}
