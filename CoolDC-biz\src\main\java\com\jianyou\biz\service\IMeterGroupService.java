package com.jianyou.biz.service;

import java.util.List;

/**
 * 表具分组关联Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IMeterGroupService {

    /**
     * 根据分组ID查询关联的表具ID列表
     *
     * @param groupId 分组ID
     * @return 表具ID列表
     */
    List<String> getMeterIdsByGroupId(String groupId);

    /**
     * 批量保存表具与分组关联
     *
     * @param groupId 分组ID
     * @param meterIds 表具ID列表
     * @return 是否成功
     */
    Boolean saveBatch(String groupId, List<String> meterIds);

    /**
     * 删除表具与分组关联
     *
     * @param groupId 分组ID
     * @return 是否成功
     */
    Boolean deleteByGroupId(String groupId);
}
