package com.jianyou.biz.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目信息视图对象 biz_project
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
public class ProjectVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String name;

    /**
     * 建设单位id
     */
    @ExcelProperty(value = "建设单位id")
    private Long deptId;

    /**
     * 项目地址
     */
    @ExcelProperty(value = "项目地址")
    private String address;

    /**
     * 项目概括
     */
    @ExcelProperty(value = "项目概括")
    private String detail;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    private String contacts;

    /**
     * 联系人电话
     */
    @ExcelProperty(value = "联系人电话")
    private String contactsNumber;

    /**
     * 施工开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "施工开始时间")
    private Date startTime;

    /**
     * 施工截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "施工截止时间")
    private Date endTime;


}
