package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.validate.QueryGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 能源汇总bo
 *
 * <AUTHOR>
 * @date 2025/07/03
 */
@Data
public class EnergySummaryBO {

    @NotBlank(message = "表具不能为空", groups = { QueryGroup.class })
    private String meterId;
    @NotBlank(message = "时间不能为空", groups = { QueryGroup.class })
    private String time;
    @NotBlank(message = "分析方式不能为空", groups = { QueryGroup.class })
    private String analysisType;
    @NotBlank(message = "点位类型不能为空", groups = { QueryGroup.class })
    private String pointType;

}
