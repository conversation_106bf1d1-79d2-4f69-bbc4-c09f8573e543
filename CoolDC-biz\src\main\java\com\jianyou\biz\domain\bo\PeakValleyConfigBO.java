package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 分段配置业务对象 enms_ladder_config
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PeakValleyConfigBO extends BaseEntity {

    /**
     * ID
     */
    private String id;

    /**
     * 计费配置id
     */
    private String billingConfigId;

    /**
     * 开始时间 HH:mm:ss
     */
    private String startTime;

    /**
     * 结束时间 HH:mm:ss
     */
    private String endTime;

    /**
     * 类型(0平;1谷;2峰)
     */
    private String type;

    /**
     * 单价
     */
    private BigDecimal price;


}
