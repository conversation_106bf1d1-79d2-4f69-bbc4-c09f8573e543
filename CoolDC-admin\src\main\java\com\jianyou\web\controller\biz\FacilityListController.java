package com.jianyou.web.controller.biz;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.jianyou.common.core.domain.BaseOptionsVO;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.utils.poi.ExcelUtil;
import com.jianyou.biz.domain.vo.FacilityListVO;
import com.jianyou.biz.domain.bo.FacilityListBO;
import com.jianyou.biz.service.IFacilityListService;
import com.jianyou.common.core.page.TableDataInfo;

/**
 * 设备清单
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/facilityList")
public class FacilityListController extends BaseController {

    private final IFacilityListService facilityListService;

    /**
     * 查询设备清单列表
     */
    @SaCheckPermission("biz:facilityList:list")
    @GetMapping("/list")
    public TableDataInfo<FacilityListVO> list(FacilityListBO bo, PageQuery pageQuery) {
        return facilityListService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备清单列表
     */
    @SaCheckPermission("biz:facilityList:export")
    @PostMapping("/export")
    public void export(FacilityListBO bo, HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "设备清单", FacilityListVO.class, response);
    }

    /**
     * 获取设备清单详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:facilityList:query")
    @GetMapping("/{id}")
    public R<FacilityListVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(facilityListService.queryById(id));
    }

    /**
     * 新增设备清单
     */
    @SaCheckPermission("biz:facilityList:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FacilityListBO bo) {
        return toAjax(facilityListService.insertByBo(bo));
    }

    /**
     * 修改设备清单
     */
    @SaCheckPermission("biz:facilityList:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FacilityListBO bo) {
        return toAjax(facilityListService.updateByBo(bo));
    }

    /**
     * 删除设备清单
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:facilityList:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(facilityListService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 设备清单选项
     */
    @GetMapping("/option")
    public R<List<BaseOptionsVO>> option(FacilityListBO bo) {
        return R.ok(facilityListService.option(bo));
    }
}
