package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.jianyou.common.core.domain.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 设备请领对象 biz_facility_get_storage
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_facility_get_storage")
public class FacilityGetStorageDO extends BaseDO {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private String id;
    /**
     * 请领人
     */
    private String getPrincipal;
    /**
     * 描述
     */
    private String details;
    /**
     * 请领时间
     */
    private Date drawTime;
    /**
     * 项目id
     */
    private String projectId;

}
