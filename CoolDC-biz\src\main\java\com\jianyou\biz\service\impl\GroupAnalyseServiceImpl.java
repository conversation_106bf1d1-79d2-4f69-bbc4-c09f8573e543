package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.GroupDO;
import com.jianyou.biz.domain.MeterDO;
import com.jianyou.biz.domain.MeterGroupDO;
import com.jianyou.biz.domain.bo.GroupAnalyseBO;
import com.jianyou.biz.domain.vo.EnergyAnalyseVO;
import com.jianyou.biz.domain.vo.EnergyRatioVO;
import com.jianyou.biz.mapper.GroupMapper;
import com.jianyou.biz.mapper.MeterEnergyMapper;
import com.jianyou.biz.mapper.MeterGroupMapper;
import com.jianyou.biz.mapper.MeterMapper;
import com.jianyou.biz.service.IGroupAnalyseService;
import com.jianyou.biz.utils.EnergyAnalyseUtils;
import com.jianyou.common.enums.AnalysisType;
import com.jianyou.common.utils.TimeFormatUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分组分析服务
 *
 * <AUTHOR>
 * @date 2025/07/05
 */
@Service
@RequiredArgsConstructor
public class GroupAnalyseServiceImpl implements IGroupAnalyseService {

    private final MeterGroupMapper meterGroupMapper;
    private final GroupMapper groupMapper;
    private final MeterMapper meterMapper;
    private final MeterEnergyMapper meterEnergyMapper;

    /**
     * 能耗分析
     */
    @Override
    public EnergyAnalyseVO energyAnalyse(GroupAnalyseBO bo) {
        EnergyAnalyseVO energyAnalyseVO = new EnergyAnalyseVO();
        energyAnalyseVO.init();

        List<String> meterNumbers = getMeterNumbers(bo.getGroupId());
        if (CollUtil.isEmpty(meterNumbers)) {
            return energyAnalyseVO;
        }

        AnalysisType analysisType = AnalysisType.getNameByValue(bo.getAnalysisType());
        String startTime = bo.getStartTime();
        String endTime = bo.getEndTime();

        // 起始时间向前偏移一个时间单位,这样可以获取能耗和环比
        String qoqStartTime = TimeFormatUtil.offsetTime(startTime, analysisType, -1);
        Date[] qoqRange = TimeFormatUtil.formatRange(qoqStartTime, endTime, analysisType, false);

        // 使用meterEnergyMapper查询能耗数据
        Map<String, Object> sumMap = EnergyAnalyseUtils.collectEnergyFromDB(meterEnergyMapper, meterNumbers, analysisType, qoqRange);
        if (CollUtil.isEmpty(sumMap)) {
            return energyAnalyseVO;
        }

        // 构建当前能耗和环比能耗 时间
        EnergyAnalyseUtils.buildTimeEnergy(sumMap, energyAnalyseVO);

        // 起始时间和截止时间都减少一年,可以获取同比
        String yoyStartTime = TimeFormatUtil.offsetTime(startTime, AnalysisType.YEARLY, -1);
        String yoyEndTime = TimeFormatUtil.offsetTime(endTime, AnalysisType.YEARLY, -1);
        Date[] yoyRange = TimeFormatUtil.formatRange(yoyStartTime, yoyEndTime, analysisType, false);

        // 使用meterEnergyMapper查询同比数据
        Map<String, Object> yoySumMap = EnergyAnalyseUtils.collectEnergyFromDB(meterEnergyMapper, meterNumbers, analysisType, yoyRange);
        LinkedList<Double> yoyEnergy = new LinkedList<>();
        yoySumMap.forEach((k, v) -> {
            yoyEnergy.add(Convert.toDouble(v));
        });
        energyAnalyseVO.setYoyEnergy(yoyEnergy);
        return energyAnalyseVO;
    }

    /**
     * 能耗占比
     */
    @Override
    public List<EnergyRatioVO> energyRatio(GroupAnalyseBO bo) {
        List<GroupDO> groups = groupMapper.selectList();
        return groups.stream().map(group -> {
            EnergyRatioVO vo = new EnergyRatioVO();
            vo.setName(group.getName());
            vo.setUnique(bo.getGroupId().equals(group.getId()));
            vo.setValue(0.0);

            List<String> meterNumbers = getMeterNumbers(group.getId());
            if (CollUtil.isEmpty(meterNumbers)) {
                return vo;
            }

            Date[] range = TimeFormatUtil.formatRange(bo.getStartTime(), bo.getEndTime(), AnalysisType.getNameByValue(bo.getAnalysisType()), false);

            // 使用meterEnergyMapper查询能耗数据并计算总和
            BigDecimal totalEnergy = EnergyAnalyseUtils.calculateTotalEnergyFromDB(meterEnergyMapper, meterNumbers, range[0], range[1]);
            vo.setValue(totalEnergy.doubleValue());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取仪表编号
     *
     * @param groupId 组id
     * @return {@link List }<{@link String }>
     */
    private List<String> getMeterNumbers(String groupId) {
        List<MeterGroupDO> meterGroups = meterGroupMapper.selectList(Wrappers.<MeterGroupDO>lambdaQuery()
            .eq(MeterGroupDO::getGroupId, groupId)
        );
        if (CollUtil.isEmpty(meterGroups)) {
            return new ArrayList<>();
        }
        List<String> meterIds = meterGroups.stream()
            .map(MeterGroupDO::getMeterId)
            .collect(Collectors.toList());
        List<MeterDO> meters = meterMapper.selectBatchIds(meterIds);
        if (CollUtil.isEmpty(meters)) {
            return new ArrayList<>();
        }
        return meters.stream().map(MeterDO::getNumber).collect(Collectors.toList());
    }


}
