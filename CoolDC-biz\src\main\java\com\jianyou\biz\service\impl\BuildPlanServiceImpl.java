package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.BuildPlanDO;
import com.jianyou.biz.domain.ProjectDO;
import com.jianyou.biz.mapper.ProjectMapper;
import com.jianyou.common.core.domain.BaseOptionsVO;
import com.jianyou.common.exception.ServiceException;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.jianyou.biz.mapper.BuildPlanMapper;
import com.jianyou.biz.service.IBuildPlanService;
import com.jianyou.biz.domain.vo.BuildPlanVO;
import com.jianyou.biz.domain.bo.BuildPlanBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 施工计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RequiredArgsConstructor
@Service
public class BuildPlanServiceImpl implements IBuildPlanService {

    private final BuildPlanMapper baseMapper;
    private final ProjectMapper projectMapper;

    /**
     * 查询施工计划列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link BuildPlanVO }>
     */
    @Override
    public TableDataInfo<BuildPlanVO> queryPageList(BuildPlanBO bo, PageQuery pageQuery) {
        IPage<BuildPlanVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<BuildPlanDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getName()), BuildPlanDO::getName, bo.getName())
            .eq(StrUtil.isNotEmpty(bo.getProjectId()), BuildPlanDO::getProjectId, bo.getProjectId())
        );
        List<BuildPlanVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new TableDataInfo<>();
        }
        Map<String, ProjectDO> map = projectMapper.nameDict(records.stream().map(BuildPlanVO::getProjectId).collect(Collectors.toList()));
        records.forEach(record -> {
            ProjectDO projectDO = map.get(record.getProjectId());
            if (projectDO != null) {
                record.setProjectName(projectDO.getName());
            }
        });
        return TableDataInfo.build(page);
    }

    /**
     * 查询施工计划
     *
     * @param id id
     * @return {@link BuildPlanVO }
     */
    @Override
    public BuildPlanVO queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增施工计划
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean insertByBo(BuildPlanBO bo) {
        BuildPlanDO buildPlanDO = new BuildPlanDO();
        BeanCopyUtils.copy(bo, buildPlanDO);
        buildPlanDO.create();
        buildPlanDO.update();
        return baseMapper.insert(buildPlanDO) > 0;
    }

    /**
     * 修改施工计划
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(BuildPlanBO bo) {
        BuildPlanDO buildPlanDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(buildPlanDO)) {
            return false;
        }
        BeanCopyUtils.copy(bo, buildPlanDO);
        buildPlanDO.update();
        return baseMapper.updateById(buildPlanDO) > 0;
    }

    /**
     * 校验并批量删除施工计划信息
     *
     * @param ids     主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 施工计划选项
     */
    @Override
    public List<BaseOptionsVO> option(BuildPlanBO bo) {
        List<BuildPlanDO> list = baseMapper.selectList(Wrappers.<BuildPlanDO>lambdaQuery()
            .eq(StrUtil.isNotEmpty(bo.getProjectId()), BuildPlanDO::getProjectId, bo.getProjectId())
            .select(BuildPlanDO::getId, BuildPlanDO::getName)
        );
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(item -> new BaseOptionsVO(item.getId(), item.getName())).collect(Collectors.toList());
    }

    /**
     * 计划变更
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void planChange(String buildPlanId, String changeStatus) {
        BuildPlanDO buildPlanDO = baseMapper.selectById(buildPlanId);
        if (ObjUtil.isEmpty(buildPlanDO)) {
            throw new ServiceException("施工计划不存在");
        }
        buildPlanDO.setChangeStatus(changeStatus);
        buildPlanDO.update();
        baseMapper.updateById(buildPlanDO);
    }

}
