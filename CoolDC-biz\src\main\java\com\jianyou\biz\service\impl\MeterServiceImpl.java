package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.AreaDO;
import com.jianyou.biz.domain.MeterDO;
import com.jianyou.biz.domain.bo.FeeBO;
import com.jianyou.biz.domain.bo.MeterBO;
import com.jianyou.biz.domain.vo.AreaVO;
import com.jianyou.biz.domain.vo.MeterVO;
import com.jianyou.biz.mapper.AreaMapper;
import com.jianyou.biz.mapper.MeterMapper;
import com.jianyou.biz.service.IMeterAccountLogService;
import com.jianyou.biz.service.IMeterReadingService;
import com.jianyou.biz.service.IMeterService;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.exception.ServiceException;
import com.jianyou.common.utils.BeanCopyUtils;
import com.jianyou.common.utils.redis.RedisUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;

/**
 * 表具Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RequiredArgsConstructor
@Service
public class MeterServiceImpl implements IMeterService {

    private final MeterMapper baseMapper;
    private final AreaMapper areaMapper;
    private final IMeterReadingService meterReadingService;
    private final IMeterAccountLogService meterAccountLogService;

    /**
     * 查询表具列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @param isPage    是否分页
     * @return {@link TableDataInfo }<{@link MeterVO }>
     */
    @Override
    public TableDataInfo<MeterVO> queryPageList(MeterBO bo, PageQuery pageQuery, boolean isPage) {
        List<MeterVO> records;
        long total;
        if (isPage) {
            IPage<MeterVO> page = baseMapper.selectVoPage(pageQuery.build(), getWrapper(bo));
            total = page.getTotal();
            records = page.getRecords();
        } else {
            records = baseMapper.selectVoList(getWrapper(bo));
            total = records.size();
        }
        if (CollUtil.isEmpty(records)) {
            return new TableDataInfo<>();
        }
        records.forEach(record -> {
            record.setAreaName(areaMapper.getAreaFullPath(record.getAreaId()));
        });
        return TableDataInfo.build(records, total);
    }

    private LambdaQueryWrapper<MeterDO> getWrapper(MeterBO bo) {
        return Wrappers.<MeterDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getName()), MeterDO::getName, bo.getName())
            .like(StrUtil.isNotEmpty(bo.getNumber()), MeterDO::getNumber, bo.getNumber())
            .like(StrUtil.isNotEmpty(bo.getRoomNumber()), MeterDO::getRoomNumber, bo.getRoomNumber())
            .eq(StrUtil.isNotEmpty(bo.getMeterStatus()), MeterDO::getMeterStatus, bo.getMeterStatus())
            .eq(StrUtil.isNotEmpty(bo.getOnlineStatus()), MeterDO::getOnlineStatus, bo.getOnlineStatus())
            .eq(StrUtil.isNotEmpty(bo.getEnergyType()), MeterDO::getEnergyType, bo.getEnergyType())
            .eq(StrUtil.isNotEmpty(bo.getEmphasisFlag()), MeterDO::getEmphasisFlag, bo.getEmphasisFlag())
            .eq(StrUtil.isNotEmpty(bo.getAreaId()), MeterDO::getAreaId, bo.getAreaId())
            .eq(StrUtil.isNotEmpty(bo.getGatewayId()), MeterDO::getGatewayId, bo.getGatewayId());
    }

    /**
     * 查询表具
     *
     * @param id id
     * @return {@link MeterVO }
     */
    @Override
    public MeterVO queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增表具
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean insertByBo(MeterBO bo) {
        MeterDO meterDO = new MeterDO();
        BeanCopyUtils.copy(bo, meterDO);
        meterDO.create();
        meterDO.update();
        meterDO.setBalance(BigDecimal.ZERO);
        meterDO.setAutoPayFlag("0");
        meterDO.setOnlineStatus("0");
        return baseMapper.insert(meterDO) > 0;
    }

    /**
     * 修改表具
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(MeterBO bo) {
        MeterDO meterDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(meterDO)) {
            return false;
        }
        BeanCopyUtils.copy(bo, meterDO);
        meterDO.update();
        return baseMapper.updateById(meterDO) > 0;
    }

    /**
     * 校验并批量删除表具信息
     *
     * @param ids     主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 查询区域表具树
     *
     * @return 区域表具树结构
     */
    @Override
    public List<Tree<String>> queryAreaMeterTree(String energyType) {
        // 1. 查询所有区域
        List<AreaVO> areas = areaMapper.selectVoList(Wrappers.<AreaDO>lambdaQuery()
            .orderByAsc(AreaDO::getWeight));
        if (CollUtil.isEmpty(areas)) {
            return new ArrayList<>();
        }

        // 2. 构建区域树节点
        List<TreeNode<String>> treeNodes = new ArrayList<>();

        // 添加区域节点
        for (AreaVO area : areas) {
            Map<String, Object> extraMap = new HashMap<>();
            extraMap.put("name", area.getName());
            // 标记为区域节点
            extraMap.put("type", "area");
            extraMap.put("weight", area.getWeight());

            TreeNode<String> areaNode = new TreeNode<String>()
                .setId(area.getId())
                .setParentId(area.getParentId())
                .setWeight(area.getWeight())
                .setExtra(extraMap);

            treeNodes.add(areaNode);
        }

        // 3. 查询符合条件的表具
        List<MeterVO> meters = baseMapper.selectVoList(Wrappers.<MeterDO>lambdaQuery()
            .eq(StrUtil.isNotEmpty(energyType), MeterDO::getEnergyType, energyType)
        );

        if (!CollUtil.isEmpty(meters)) {
            // 4. 将表具添加为区域的子节点
            for (MeterVO meter : meters) {
                // 如果表具没有关联区域，则跳过
                if (StrUtil.isEmpty(meter.getAreaId())) {
                    continue;
                }

                Map<String, Object> extraMap = new HashMap<>();
                extraMap.put("name", meter.getName());
                // 标记为表具节点
                extraMap.put("type", "meter");

                TreeNode<String> meterNode = new TreeNode<String>()
                    .setId(meter.getId())
                    .setParentId(meter.getAreaId())
                    .setWeight(0L)
                    .setExtra(extraMap);

                treeNodes.add(meterNode);
            }
        }

        // 5. 构建树
        return TreeUtil.build(treeNodes, "0");
    }

    /**
     * 预存
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean topUp(FeeBO bo) {
        MeterDO meterDO = baseMapper.selectById(bo.getMeterId());
        if (ObjUtil.isEmpty(meterDO)) {
            return false;
        }
        BigDecimal balance = meterDO.getBalance();
        // 更新表具余额
        meterDO.setBalance(bo.getAmount().add(balance));
        meterDO.update();
        baseMapper.updateById(meterDO);
        // 使用通用的流水记录方法
        return meterAccountLogService.recordAccountChange(
            meterDO.getId(),
            balance,
            bo.getAmount(),
            "1",
            "表具预存充值"
        );
    }

    /**
     * 退费
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean returnFee(FeeBO bo) {
        MeterDO meterDO = baseMapper.selectById(bo.getMeterId());
        if (ObjUtil.isEmpty(meterDO)) {
            return false;
        }
        BigDecimal balance = meterDO.getBalance();
        if (balance.compareTo(bo.getAmount()) < 0) {
            throw new ServiceException("表具余额不足");
        }
        // 减去表具余额
        meterDO.setBalance(balance.subtract(bo.getAmount()));
        meterDO.update();
        baseMapper.updateById(meterDO);
        // 使用通用的流水记录方法
        return meterAccountLogService.recordAccountChange(
            meterDO.getId(),
            balance,
            bo.getAmount(),
            "3",
            "表具退费"
        );
    }

    /**
     * 修改自动退费
     */
    @Override
    public Boolean editAutoPay(FeeBO bo) {
        MeterDO meterDO = baseMapper.selectById(bo.getMeterId());
        if (ObjUtil.isEmpty(meterDO)) {
            return false;
        }
        meterDO.setAutoPayFlag(bo.getAutoPayFlag());
        meterDO.update();
        return baseMapper.updateById(meterDO) > 0;
    }

    /**
     * 抄表
     *
     * @param meterIds   仪表id
     * @param readingWay 抄表方式 1手动 0自动
     * @param isBilling  是否计费
     */
    @Override
    public Boolean reading(List<String> meterIds, String readingWay, boolean isBilling) {
        Object lock = RedisUtils.getCacheObject("meter:reading:lock");
        if (lock != null) {
            throw new ServiceException("抄表正在进行中...请耐心等待~");
        }
        RedisUtils.setCacheObject("meter:reading:lock", "1", Duration.ofHours(23));
        meterReadingService.reading(meterIds, readingWay, isBilling);
        return true;
    }
}
