package com.jianyou.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.log.StaticLog;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.BillDO;
import com.jianyou.biz.domain.BillDetailDO;
import com.jianyou.biz.domain.MeterDO;
import com.jianyou.biz.domain.bo.BillBO;
import com.jianyou.biz.domain.bo.payBO;
import com.jianyou.biz.domain.dto.*;
import com.jianyou.biz.domain.vo.BillVO;
import com.jianyou.biz.domain.vo.BillingConfigVO;
import com.jianyou.biz.domain.vo.PeakValleyConfigVO;
import com.jianyou.biz.domain.vo.TieredConfigVO;
import com.jianyou.biz.mapper.BillDetailMapper;
import com.jianyou.biz.mapper.BillMapper;
import com.jianyou.biz.service.IBillService;
import com.jianyou.biz.service.IBillingConfigService;
import com.jianyou.biz.service.IMeterAccountLogService;
import com.jianyou.biz.utils.MeterBillingCalculatorUtils;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.enums.EnergyTypeEnum;
import com.jianyou.common.enums.InfluxdbField;
import com.jianyou.common.enums.InfluxdbMeasurement;
import com.jianyou.common.exception.ServiceException;
import com.jianyou.common.utils.influxdb.InfluxUtils;
import com.jianyou.common.utils.influxdb.domain.dto.InfluxRes;
import com.jianyou.common.utils.influxdb.domain.dto.TimeValue;
import com.jianyou.common.utils.influxdb.domain.dto.TimeValueList;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@RequiredArgsConstructor
@Service
public class BillServiceImpl implements IBillService {

    private final BillMapper baseMapper;
    private final BillDetailMapper billDetailMapper;
    private final InfluxUtils influxUtils;
    private final IBillingConfigService billingConfigService;
    private final IMeterAccountLogService meterAccountLogService;

    /**
     * 查询账单列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @param isPage    是否分页
     * @return {@link TableDataInfo }<{@link BillVO }>
     */
    @Override
    public TableDataInfo<BillVO> queryPageList(BillBO bo, PageQuery pageQuery, boolean isPage) {
        List<BillVO> records;
        long total;
        if (isPage) {
            IPage<BillVO> page = baseMapper.selectVoPage(pageQuery.build(), getWrapper(bo));
            total = page.getTotal();
            records = page.getRecords();
        } else {
            records = baseMapper.selectVoList(getWrapper(bo));
            total = records.size();
        }
        if (CollUtil.isEmpty(records)) {
            return new TableDataInfo<>();
        }
        return TableDataInfo.build(records, total);
    }

    private LambdaQueryWrapper<BillDO> getWrapper(BillBO bo) {
        return Wrappers.<BillDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getMeterName()), BillDO::getMeterName, bo.getMeterName())
            .eq(StrUtil.isNotEmpty(bo.getMeterId()), BillDO::getMeterId, bo.getMeterId())
            .eq(StrUtil.isNotEmpty(bo.getBillNo()), BillDO::getBillNo, bo.getBillNo())
            .eq(StrUtil.isNotEmpty(bo.getEnergyType()), BillDO::getEnergyType, bo.getEnergyType())
            .eq(StrUtil.isNotEmpty(bo.getRateType()), BillDO::getRateType, bo.getRateType())
            .eq(StrUtil.isNotEmpty(bo.getPeakValleyFlag()), BillDO::getPeakValleyFlag, bo.getPeakValleyFlag())
            .eq(StrUtil.isNotEmpty(bo.getPayStatus()), BillDO::getPayStatus, bo.getPayStatus())
            .between(ObjUtil.isNotEmpty(bo.getStartThisTime()) && ObjUtil.isNotEmpty(bo.getEndThisTime()),
                BillDO::getThisTime, bo.getStartThisTime(), bo.getEndThisTime())
            .orderByDesc(BillDO::getThisTime)
            .orderByAsc(BillDO::getPayStatus);
    }

    /**
     * 查询账单
     *
     * @param id id
     * @return {@link BillVO }
     */
    @Override
    public BillVO queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 缴费
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pay(payBO bo) {
        String id = bo.getBillId();
        BillDO billDO = baseMapper.selectById(id);
        if (billDO == null) {
            return false;
        }
        if ("1".equals(billDO.getPayStatus())) {
            throw new ServiceException("当前账单已缴费,请抄表");
        }
        billDO.setPayTime(new Date());
        billDO.setPayStatus("1");
        // TODO 扣费业务

        baseMapper.updateById(billDO);
        return true;
    }

    /**
     * 生成账单
     *
     * @param meters     表具
     * @param readingWay 抄表方式
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void generateBill(List<MeterDO> meters, String readingWay) {
        Date lastTime = new Date();
        Date now = new Date();
        List<String> numbers = meters.stream().map(MeterDO::getNumber).collect(Collectors.toList());
        // 水电气配置
        BillingConfigVO waterConfig = billingConfigService.queryConfig(EnergyTypeEnum.WATER.getValue(), now);
        BillingConfigVO electricityConfig = billingConfigService.queryConfig(EnergyTypeEnum.ELECTRICITY.getValue(), now);
        BillingConfigVO gasConfig = billingConfigService.queryConfig(EnergyTypeEnum.GAS.getValue(), now);

        // 表具账单
        List<String> meterIds = meters.stream().map(MeterDO::getId).collect(Collectors.toList());
        List<BillDO> lastBills = baseMapper.selectList(Wrappers.<BillDO>lambdaQuery()
            .in(BillDO::getMeterId, meterIds)
            .orderByDesc(BillDO::getThisTime)
        );
        // 获取各个表具的账单
        Map<String, List<BillDO>> meterBillMap = lastBills.stream().collect(Collectors.groupingBy(BillDO::getMeterId));

        // 获取每个表具中最新的一次账单,并获取这些账单中最新的抄表时间
        BillDO maxBill = meterBillMap.values().stream()
            .filter(list -> list != null && !list.isEmpty())
            .map(list -> list.get(0))
            .filter(bill -> bill.getPayStatus() != null && !"0".equals(bill.getPayStatus()))
            .max(Comparator.comparing(BillDO::getThisTime))
            .orElse(null);
        if (maxBill != null) {
            lastTime = maxBill.getThisTime();
        }

        // 表具能耗
        Map<String, List<InfluxRes>> energyMap = influxUtils.selectBatch(InfluxdbMeasurement.METER,
            numbers,
            Collections.singletonList(InfluxdbField.READING),
            lastTime,
            now,
            false,
            -3);
        if (CollUtil.isEmpty(energyMap)) {
            StaticLog.error("最近3天暂无能耗数据");
            return;
        }

        meters.forEach(meter -> {
            String energyType = meter.getEnergyType();
            String autoPayFlag = meter.getAutoPayFlag();
            BigDecimal balance = meter.getBalance();
            EnergyTypeEnum energyTypeEnum = EnergyTypeEnum.fromValue(energyType);
            String energyUnit = energyTypeEnum.getUnit();
            BillingConfigVO config;
            switch (energyTypeEnum) {
                case WATER:
                    config = waterConfig;
                    break;
                case ELECTRICITY:
                    config = electricityConfig;
                    break;
                case GAS:
                    config = gasConfig;
                    break;
                default:
                    return;
            }
            if (config == null) {
                return;
            }
            // 计费周期
            Date startTime = config.getStartTime();
            Date endTime = config.getEndTime();
            // 配置版本号
            String configVersions = config.getConfigVersions();
            // 获取当前表具的能耗数据
            List<InfluxRes> resList = energyMap.get(meter.getNumber());
            if (resList == null) {
                StaticLog.error("表具:{},最近3天暂无能耗数据,跳过计费", meter.getName());
                return;
            }
            InfluxRes thisRes = resList.get(resList.size() - 1);
            // 本次读数时间
            Date thisTime = DateUtil.parseDateTime(thisRes.getTime());
            // 本次读数
            BigDecimal thisReading = new BigDecimal(thisRes.getValue());
            // 获取当前表具的账单
            List<BillDO> meterLastBills = meterBillMap.get(meter.getId());
            if (meterLastBills == null) {
                // 第一次抄表 使用当前时间作为上次抄表时间和本次抄表时间
                BillDO bill = new BillDO();
                bill.setBillNo(System.currentTimeMillis() + "");
                bill.setRateType(config.getRateType());
                bill.setPeakValleyFlag(config.getPeakValleyFlag());
                bill.setMeterId(meter.getId());
                bill.setMeterName(meter.getName());
                bill.setEnergyType(energyType);
                bill.setEnergyUnit(energyUnit);
                bill.setReadingWay(readingWay);
                bill.setThisTime(thisTime);
                bill.setThisReading(thisReading);
                bill.setLastTime(thisTime);
                bill.setLastReading(thisReading);
                bill.setEnergyVolume(BigDecimal.ZERO);
                bill.setSettlementAmount(BigDecimal.ZERO);
                bill.setPayStatus("1");
                bill.setPayWay("2");
                bill.setPayTime(now);
                bill.setCreateTime(now);
                // 阶梯累计量
                bill.setFinalCumulativeVolume(BigDecimal.ZERO);
                // 计费周期
                bill.setCycles(DateUtil.formatDateTime(startTime) + " ~ " + DateUtil.formatDateTime(endTime));
                // 配置版本号
                bill.setConfigVersions(configVersions);
                baseMapper.insert(bill);
                return;
            }
            // 获取当前表具最近一条账单
            BillDO lastBill = meterLastBills.get(0);
            // 判断是否缴费 未缴则不生成新账单
            if ("0".equals(lastBill.getPayStatus())) {
                BigDecimal settlementAmount = lastBill.getSettlementAmount();
                // 则判断是否为自动缴费
                if ("1".equals(autoPayFlag)) {
                    if (balance.compareTo(settlementAmount) > 0) {
                        // 账户余额足够 直接自动扣费
                        lastBill.setPayStatus("1");
                        lastBill.setPayWay("2");
                        lastBill.setPayTime(now);
                        meter.setBalance(balance.subtract(settlementAmount));
                        meter.update();
                        // 记录流水
                        meterAccountLogService.recordAccountChange(
                            meter.getId(),
                            balance,
                            settlementAmount,
                            "2",
                            "自动扣费"
                        );
                        baseMapper.updateById(lastBill);
                    }
                }
                return;
            }
            Date lastThisTime = lastBill.getThisTime();
            BigDecimal lastThisReading = lastBill.getThisReading();
            // 找到上次抄表时记录的那个数据点
            // 1. 预处理,转成时间值对象
            List<TimeValue> timeValues = resList.stream()
                .map(r -> new TimeValue(DateUtil.parseDateTime(r.getTime()), new BigDecimal(r.getValue())))
                .collect(Collectors.toList());
            // 构造为时间值列表使用二分查找
            TimeValueList timeValueList = new TimeValueList(timeValues);
            TimeValue nearestValue = timeValueList.findNearestValue(lastThisTime);
            if (nearestValue == null) {
                // 没找到就返回
                return;
            }
            // 如果找到，则保留从该数据点开始的部分
            resList = resList.subList(nearestValue.getIndex(), resList.size());

            if (resList.size() < 2) {
                return;
            }
            // 生成新账单
            BillDO newBill = new BillDO();
            newBill.setBillNo(System.currentTimeMillis() + "");
            newBill.setMeterId(meter.getId());
            newBill.setMeterName(meter.getName());
            newBill.setEnergyType(energyType);
            newBill.setEnergyUnit(energyUnit);
            newBill.setReadingWay(readingWay);
            newBill.setLastReading(lastThisReading);
            newBill.setLastTime(lastThisTime);
            newBill.setThisReading(thisReading);
            newBill.setThisTime(thisTime);
            newBill.setRateType(config.getRateType());
            newBill.setPeakValleyFlag(config.getPeakValleyFlag());
            newBill.setPayStatus("0");
            newBill.setCreateTime(now);
            // 配置版本号
            newBill.setConfigVersions(configVersions);
            // 计费周期
            newBill.setCycles(DateUtil.formatDateTime(startTime) + " ~ " + DateUtil.formatDateTime(endTime));

            // 判断上一个账单的配置版本号是否和当前获取的配置版本一致
            BigDecimal finalCumulativeVolume = lastBill.getFinalCumulativeVolume();
            if (!lastBill.getConfigVersions().equals(configVersions)) {
                // 如果不一致,则清空阶梯累计量
                finalCumulativeVolume = BigDecimal.ZERO;
            }

            // 计算结算金额
            BillingResult result = calculateAmount(config, resList, finalCumulativeVolume);
            BigDecimal settlementAmount = result.getTotalFee();
            newBill.setSettlementAmount(settlementAmount);
            // 阶梯累计量
            newBill.setFinalCumulativeVolume(result.getFinalCumulativeVolume());
            // 能耗量
            newBill.setEnergyVolume(result.getTotalVolume());
            // 判断是否为自动缴费
            if ("1".equals(autoPayFlag)) {
                if (balance.compareTo(settlementAmount) > 0) {
                    // 账户余额足够 直接自动扣费
                    newBill.setPayStatus("1");
                    newBill.setPayWay("2");
                    newBill.setPayTime(now);
                    meter.setBalance(balance.subtract(settlementAmount));
                    meter.update();
                    // 记录流水
                    meterAccountLogService.recordAccountChange(
                        meter.getId(),
                        balance,
                        settlementAmount,
                        "2",
                        "自动扣费"
                    );
                }
            }
            baseMapper.insert(newBill);

            List<ChargeDetail> details = result.getDetails();
            if (CollUtil.isEmpty(details)) {
                return;
            }
            List<BillDetailDO> billDetails = BeanUtil.copyToList(details, BillDetailDO.class);
            billDetails.forEach(billDetail -> {
                billDetail.setBillId(newBill.getId());
                billDetail.setEnergyUnit(energyUnit);
            });
            billDetailMapper.insertBatch(billDetails);
        });
    }

    /**
     * 计算金额
     *
     * @param config  配置
     * @param resList 读数列表
     * @return {@link BillingResult } 结算结果
     */
    private BillingResult calculateAmount(BillingConfigVO config, List<InfluxRes> resList, BigDecimal finalCumulativeVolume) {
        String rateType = config.getRateType();
        String peakValleyFlag = config.getPeakValleyFlag();
        BigDecimal standardPrice = config.getStandardPrice();

        List<MeterReading> readings = resList.stream()
            .map(res -> new MeterReading(new BigDecimal(res.getValue()), DateUtil.parseLocalDateTime(res.getTime())))
            .collect(Collectors.toList());

        List<TieredConfigVO> tieredConfigs = config.getTieredConfigs();
        if (tieredConfigs == null) {
            tieredConfigs = new ArrayList<>();
        }
        List<Ladder> ladderList = tieredConfigs.stream()
            .map(item -> new Ladder(item.getEndEnergy(), item.getRate(), "阶段" + item.getStage()))
            .collect(Collectors.toList());


        List<PeakValleyConfigVO> peakValleyConfigs = config.getPeakValleyConfigs();
        if (peakValleyConfigs == null) {
            peakValleyConfigs = new ArrayList<>();
        }
        List<TimePrice> priceList = peakValleyConfigs.stream().map(item -> new TimePrice(LocalTime.parse(item.getStartTime()),
                LocalTime.parse(item.getEndTime()),
                item.getPrice(),
                item.getType()))
            .collect(Collectors.toList());

        return MeterBillingCalculatorUtils.calculate(readings, ladderList, priceList, standardPrice, new BillingConfig(rateType, peakValleyFlag), finalCumulativeVolume);
    }

}
