package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.jianyou.common.core.domain.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 施工计划对象 biz_build_plan
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_build_plan")
public class BuildPlanDO extends BaseDO {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private String id;
    /**
     * 项目ID
     */
    private String projectId;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String details;
    /**
     * 分项负责人
     */
    private String principal;
    /**
     * 分项负责人电话
     */
    private String principalNumber;
    /**
     * 施工开始时间
     */
    private Date startTime;
    /**
     * 施工截止时间
     */
    private Date endTime;
    /**
     * 变更状态（未确认、确认、否认、废除）
     */
    private String changeStatus;

}
