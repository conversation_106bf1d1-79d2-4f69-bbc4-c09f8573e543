package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.math.BigDecimal;

/**
 * 设备请领记录业务对象 biz_facility_get_reading
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FacilityGetReadingBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 设备请领id
     */
    @NotBlank(message = "设备请领id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String facilityGetStorageId;

    /**
     * 设备id
     */
    @NotBlank(message = "设备不能为空", groups = { AddGroup.class, EditGroup.class })
    private String facilityId;

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String facilityName;

    /**
     * 规格型号
     */
    @NotBlank(message = "规格型号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String facilityModel;

    /**
     * 厂家名称
     */
    @NotBlank(message = "厂家名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String factoryName;

    /**
     * 请领数量
     */
    @NotNull(message = "请领数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal count;


}
