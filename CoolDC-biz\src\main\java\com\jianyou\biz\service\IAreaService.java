package com.jianyou.biz.service;

import cn.hutool.core.lang.tree.Tree;
import com.jianyou.biz.domain.bo.AreaBO;
import com.jianyou.biz.domain.vo.AreaVO;

import java.util.Collection;
import java.util.List;

/**
 * 区域Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IAreaService {


    /**
     * 查询区域列表
     *
     * @param bo bo
     * @return {@link List }<{@link Tree }<{@link String }>>
     */
    List<Tree<String>> queryPageList(AreaBO bo);

    /**
     * 查询区域
     *
     * @param id id
     * @return {@link AreaVO }
     */
     AreaVO queryById(String id);

    /**
     * 新增区域
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(AreaBO bo);

    /**
     * 修改区域
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(AreaBO bo);

    /**
     * 校验并批量删除区域信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
