package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 表具分组关联视图对象 enms_meter_group
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@ExcelIgnoreUnannotated
public class MeterGroupVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 分组id
     */
    @ExcelProperty(value = "分组id")
    private String groupId;

    /**
     * 表具id
     */
    @ExcelProperty(value = "表具id")
    private String meterId;


}
