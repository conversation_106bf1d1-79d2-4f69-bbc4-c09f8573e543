package com.jianyou.biz.service;

import com.jianyou.biz.domain.vo.BuildScheduleVO;
import com.jianyou.biz.domain.bo.BuildScheduleBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.Collection;

/**
 * 施工进度Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IBuildScheduleService {


    /**
     * 查询施工进度列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link BuildScheduleVO }>
     */
    TableDataInfo<BuildScheduleVO> queryPageList(BuildScheduleBO bo, PageQuery pageQuery);

    /**
     * 查询施工进度
     *
     * @param id id
     * @return {@link BuildScheduleVO }
     */
     BuildScheduleVO queryById(String id);

    /**
     * 新增施工进度
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(BuildScheduleBO bo);

    /**
     * 修改施工进度
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(BuildScheduleBO bo);

    /**
     * 校验并批量删除施工进度信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
