package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.jianyou.common.core.domain.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 设备库存对象 biz_facility_storage
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_facility_storage")
public class FacilityStorageDO extends BaseDO {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private String id;
    /**
     * 设备id
     */
    private String facilityId;
    /**
     * 设备名称
     */
    private String facilityName;
    /**
     * 库存
     */
    private BigDecimal stock;
    /**
     * 项目id
     */
    private String projectId;

}
