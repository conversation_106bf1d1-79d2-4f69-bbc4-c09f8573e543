package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jianyou.common.annotation.ExcelDictFormat;
import com.jianyou.common.convert.ExcelDictConvert;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 表具视图对象 enms_meter
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@ExcelIgnoreUnannotated
public class MeterVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private String id;

    /**
     * 名称
     */
    @ExcelProperty(value = "表具名称")
    private String name;

    /**
     * 表具编号
     */
    @ExcelProperty(value = "表具编号")
    private String number;

    /**
     * 房号
     */
    @ExcelProperty(value = "房号")
    private String roomNumber;

    /**
     * 区域Id
     */
    private String areaId;

    @ExcelProperty(value = "区域")
    private String areaName;

    /**
     * 网关id
     */
    private String gatewayId;

    /**
     * 能源类型
     */
    @ExcelProperty(value = "能源类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "energy_type")
    private String energyType;

    /**
     * 表具状态
     */
    private String meterStatus;

    /**
     * 在线状态
     */
    @ExcelProperty(value = "在线状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "online_status")
    private String onlineStatus;

    /**
     * 表具读数
     */
    @ExcelProperty(value = "表具读数")
    private BigDecimal meterRecord;

    /**
     * 表具时间
     */
    @ExcelProperty(value = "表具时间")
    private String meterTime;

    /**
     * 同步时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncTime;

    /**
     * 是否重点表具;1是0否
     */
    private String emphasisFlag;

    /**
     * 账户余额
     */
    private BigDecimal balance;

    /**
     * 是否自动扣费;1是0否
     */
    private String autoPayFlag;

}
