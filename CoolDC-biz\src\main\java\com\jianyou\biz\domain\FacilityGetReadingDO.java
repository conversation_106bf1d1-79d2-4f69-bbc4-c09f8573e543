package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 设备请领记录对象 biz_facility_get_reading
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@TableName("biz_facility_get_reading")
public class FacilityGetReadingDO {

    /**
     * ID
     */
    private String id;
    /**
     * 设备请领id
     */
    private String facilityGetStorageId;
    /**
     * 设备id
     */
    private String facilityId;
    /**
     * 设备名称
     */
    private String facilityName;
    /**
     * 规格型号
     */
    private String facilityModel;
    /**
     * 厂家名称
     */
    private String factoryName;
    /**
     * 请领数量
     */
    private BigDecimal count;

}
