package com.jianyou.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 能源类型枚举
 *
 * <AUTHOR>
 * @date 2025/07/18
 */
@Getter
@AllArgsConstructor
public enum EnergyTypeEnum {
    WATER("1", "水", "m³"),
    ELECTRICITY("2", "电", "kWh"),
    GAS("3", "气", "m³");

    private final String value;
    private final String name;
    private final String unit;

    public static EnergyTypeEnum fromValue(String value) {
        for (EnergyTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的能源类型值: " + value);
    }

}
