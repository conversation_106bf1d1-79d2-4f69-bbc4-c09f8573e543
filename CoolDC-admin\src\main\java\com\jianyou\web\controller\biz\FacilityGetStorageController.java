package com.jianyou.web.controller.biz;

import java.util.ArrayList;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.utils.poi.ExcelUtil;
import com.jianyou.biz.domain.vo.FacilityGetStorageVO;
import com.jianyou.biz.domain.bo.FacilityGetStorageBO;
import com.jianyou.biz.service.IFacilityGetStorageService;
import com.jianyou.common.core.page.TableDataInfo;

/**
 * 设备请领
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/facilityGetStorage")
public class FacilityGetStorageController extends BaseController {

    private final IFacilityGetStorageService facilityGetStorageService;

    /**
     * 查询设备请领列表
     */
    @SaCheckPermission("biz:facilityGetStorage:list")
    @GetMapping("/list")
    public TableDataInfo<FacilityGetStorageVO> list(FacilityGetStorageBO bo, PageQuery pageQuery) {
        return facilityGetStorageService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备请领列表
     */
    @SaCheckPermission("biz:facilityGetStorage:export")
    @PostMapping("/export")
    public void export(FacilityGetStorageBO bo, HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "设备请领", FacilityGetStorageVO.class, response);
    }

    /**
     * 获取设备请领详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:facilityGetStorage:query")
    @GetMapping("/{id}")
    public R<FacilityGetStorageVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(facilityGetStorageService.queryById(id));
    }

    /**
     * 新增设备请领
     */
    @SaCheckPermission("biz:facilityGetStorage:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FacilityGetStorageBO bo) {
        return toAjax(facilityGetStorageService.insertByBo(bo));
    }

    /**
     * 修改设备请领
     */
    @SaCheckPermission("biz:facilityGetStorage:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FacilityGetStorageBO bo) {
        return toAjax(facilityGetStorageService.updateByBo(bo));
    }

    /**
     * 删除设备请领
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:facilityGetStorage:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(facilityGetStorageService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
