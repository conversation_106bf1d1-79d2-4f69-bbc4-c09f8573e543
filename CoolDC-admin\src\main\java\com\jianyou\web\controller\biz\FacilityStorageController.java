package com.jianyou.web.controller.biz;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.redisson.api.RList;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.utils.poi.ExcelUtil;
import com.jianyou.biz.domain.vo.FacilityStorageVO;
import com.jianyou.biz.domain.bo.FacilityStorageBO;
import com.jianyou.biz.service.IFacilityStorageService;
import com.jianyou.common.core.page.TableDataInfo;

/**
 * 设备库存
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/facilityStorage")
public class FacilityStorageController extends BaseController {

    private final IFacilityStorageService facilityStorageService;

    /**
     * 查询设备库存列表
     */
    @SaCheckPermission("biz:facilityStorage:list")
    @GetMapping("/list")
    public TableDataInfo<FacilityStorageVO> list(FacilityStorageBO bo, PageQuery pageQuery) {
        return facilityStorageService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询设备库存列表All
     */
    @SaCheckPermission("biz:facilityStorage:list")
    @GetMapping("/listAll")
    public R<List<FacilityStorageVO>> listAll(FacilityStorageBO bo) {
        return R.ok(facilityStorageService.listAll(bo));
    }

    /**
     * 导出设备库存列表
     */
    @SaCheckPermission("biz:facilityStorage:export")
    @PostMapping("/export")
    public void export(FacilityStorageBO bo, HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "设备库存", FacilityStorageVO.class, response);
    }

    /**
     * 获取设备库存详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:facilityStorage:query")
    @GetMapping("/{id}")
    public R<FacilityStorageVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(facilityStorageService.queryById(id));
    }

    /**
     * 新增设备库存
     */
    @SaCheckPermission("biz:facilityStorage:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FacilityStorageBO bo) {
        return toAjax(facilityStorageService.insertByBo(bo));
    }

    /**
     * 修改设备库存
     */
    @SaCheckPermission("biz:facilityStorage:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FacilityStorageBO bo) {
        return toAjax(facilityStorageService.updateByBo(bo));
    }

    /**
     * 删除设备库存
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:facilityStorage:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(facilityStorageService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
