package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.jianyou.common.core.domain.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备入库记录对象 biz_facility_put_storage
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_facility_put_storage")
public class FacilityPutStorageDO extends BaseDO {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private String id;
    /**
     * 设备id
     */
    private String facilityId;
    /**
     * 设备名称
     */
    private String facilityName;
    /**
     * 入库数量
     */
    private BigDecimal count;
    /**
     * 入库时间
     */
    private Date entryTime;
    /**
     * 项目id
     */
    private String projectId;

}
