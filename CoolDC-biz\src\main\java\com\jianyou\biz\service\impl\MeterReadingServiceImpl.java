package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.MeterDO;
import com.jianyou.biz.domain.MeterReadingDO;
import com.jianyou.biz.domain.bo.MeterReadingBO;
import com.jianyou.biz.domain.vo.MeterReadingVO;
import com.jianyou.biz.mapper.MeterMapper;
import com.jianyou.biz.mapper.MeterReadingMapper;
import com.jianyou.biz.service.IBillService;
import com.jianyou.biz.service.IMeterReadingService;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.enums.InfluxdbField;
import com.jianyou.common.enums.InfluxdbMeasurement;
import com.jianyou.common.exception.ServiceException;
import com.jianyou.common.utils.influxdb.InfluxUtils;
import com.jianyou.common.utils.influxdb.domain.dto.InfluxRes;
import com.jianyou.common.utils.redis.RedisUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 抄表记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@RequiredArgsConstructor
@Service
public class MeterReadingServiceImpl implements IMeterReadingService {

    private final MeterReadingMapper baseMapper;
    private final MeterMapper meterMapper;
    private final InfluxUtils influxUtils;
    private final IBillService billService;

    /**
     * 查询抄表记录列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @param isPage    是否分页
     * @return {@link TableDataInfo }<{@link MeterReadingVO }>
     */
    @Override
    public TableDataInfo<MeterReadingVO> queryPageList(MeterReadingBO bo, PageQuery pageQuery, boolean isPage){
        List<MeterReadingVO> records;
        long total;
        if (isPage) {
            IPage<MeterReadingVO> page = baseMapper.selectVoPage(pageQuery.build(), getWrapper(bo));
            total = page.getTotal();
            records = page.getRecords();
        } else {
            records = baseMapper.selectVoList(getWrapper(bo));
            total = records.size();
        }
        if (CollUtil.isEmpty(records)) {
            return new TableDataInfo<>();
        }
        return TableDataInfo.build(records, total);
    }

    private LambdaQueryWrapper<MeterReadingDO> getWrapper(MeterReadingBO bo) {
        return Wrappers.<MeterReadingDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getMeterName()), MeterReadingDO::getMeterName, bo.getMeterName())
            .like(StrUtil.isNotEmpty(bo.getMeterNumber()), MeterReadingDO::getMeterNumber, bo.getMeterNumber())
            .like(StrUtil.isNotEmpty(bo.getRoomNumber()), MeterReadingDO::getRoomNumber, bo.getRoomNumber())
            .eq(StrUtil.isNotEmpty(bo.getReadingWay()), MeterReadingDO::getReadingWay, bo.getReadingWay())
            .eq(StrUtil.isNotEmpty(bo.getMeterId()), MeterReadingDO::getMeterId, bo.getMeterId())
            .between(ObjUtil.isNotEmpty(bo.getStartReadingTime()) && ObjUtil.isNotEmpty(bo.getEndReadingTime()),
                MeterReadingDO::getReadingTime, bo.getStartReadingTime(), bo.getEndReadingTime())
            .orderByDesc(MeterReadingDO::getReadingTime);
    }


    /**
     * 校验并批量删除抄表记录信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid){
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    /**
     * 抄表 异步
     *
     * @param meterIds   仪表id
     * @param readingWay 抄表方式 1手动 0自动
     * @param isBilling  是否计费
     */
    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reading(List<String> meterIds, String readingWay, boolean isBilling) {
        try {
            List<MeterDO> meters = meterMapper.selectBatchIds(meterIds);
            if (CollUtil.isEmpty(meters)) {
                throw new ServiceException("表具不存在");
            }
            Date now = new Date();
            List<String> numbers = meters.stream().map(MeterDO::getNumber).collect(Collectors.toList());
            Map<String, List<InfluxRes>> resMap = influxUtils.selectBatchLatest(InfluxdbMeasurement.METER,
                numbers,
                Collections.singletonList(InfluxdbField.READING),
                null, now);
            if (CollUtil.isEmpty(resMap)) {
                throw new ServiceException("未获取到表具读数");
            }
            List<MeterReadingDO> meterReadings = meters.stream().map(meter -> {
                MeterReadingDO meterReadingDO = new MeterReadingDO();
                meterReadingDO.setMeterId(meter.getId());
                meterReadingDO.setMeterName(meter.getName());
                meterReadingDO.setMeterNumber(meter.getNumber());
                meterReadingDO.setRoomNumber(meter.getRoomNumber());
                meterReadingDO.setEnergyType(meter.getEnergyType());
                meterReadingDO.setReadingTime(now);
                meterReadingDO.setReadingWay(readingWay);
                // 获取读数
                List<InfluxRes> res = resMap.get(meter.getNumber());
                if (res != null) {
                    InfluxRes influxRes = res.get(0);
                    BigDecimal meterRecord = new BigDecimal(influxRes.getValue());
                    meterReadingDO.setMeterRecord(meterRecord);
                    meterReadingDO.setMeterTime(influxRes.getTime());

                    meter.setMeterRecord(meterRecord);
                    meter.setMeterTime(influxRes.getTime());
                    meter.setSyncTime(now);
                }
                return meterReadingDO;
            }).collect(Collectors.toList());
            baseMapper.insertBatch(meterReadings);

            if (isBilling) {
                billService.generateBill(meters, readingWay);
            }
            meterMapper.updateBatchById(meters);
        } finally {
            RedisUtils.deleteObject("meter:reading:lock");
        }
    }

}
