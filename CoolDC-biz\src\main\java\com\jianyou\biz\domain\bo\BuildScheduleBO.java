package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 施工进度业务对象 biz_build_schedule
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildScheduleBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 施工计划id
     */
    @NotBlank(message = "施工计划id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String buildPlanId;

    /**
     * 今日工作
     */
    @NotBlank(message = "今日工作不能为空", groups = { AddGroup.class, EditGroup.class })
    private String todayWork;

    /**
     * 计划工作
     */
    @NotBlank(message = "计划工作不能为空", groups = { AddGroup.class, EditGroup.class })
    private String planWork;

    /**
     * 现场照片
     */
    @NotBlank(message = "现场照片不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ossIds;

    /**
     * 项目id
     */
    @NotBlank(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectId;

    /**
     * 上报时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "上报时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date reportTime;


}
