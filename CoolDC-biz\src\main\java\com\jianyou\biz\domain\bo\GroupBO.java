package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.core.validate.QueryGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * 表具分组业务对象 enms_group
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GroupBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 能源类型
     */
    @NotBlank(message = "能源类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String energyType;

    @NotEmpty(message = "表具不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<String> meterIds;

    @NotBlank(message = "点位类型不能为空", groups = { QueryGroup.class })
    private String pointType;


}
