package com.jianyou.biz.service;

import com.jianyou.biz.domain.vo.FacilityPutStorageVO;
import com.jianyou.biz.domain.bo.FacilityPutStorageBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.Collection;

/**
 * 设备入库记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface IFacilityPutStorageService {


    /**
     * 查询设备入库记录列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link FacilityPutStorageVO }>
     */
    TableDataInfo<FacilityPutStorageVO> queryPageList(FacilityPutStorageBO bo, PageQuery pageQuery);

    /**
     * 查询设备入库记录
     *
     * @param id id
     * @return {@link FacilityPutStorageVO }
     */
     FacilityPutStorageVO queryById(String id);

    /**
     * 新增设备入库记录
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(FacilityPutStorageBO bo);

    /**
     * 修改设备入库记录
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(FacilityPutStorageBO bo);

    /**
     * 校验并批量删除设备入库记录信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
