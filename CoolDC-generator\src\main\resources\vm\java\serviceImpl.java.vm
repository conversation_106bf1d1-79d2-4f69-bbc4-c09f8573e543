package ${packageName}.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.${ClassName}DO;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.service.I${ClassName}Service;
import ${packageName}.domain.vo.${ClassName}VO;
import ${packageName}.domain.bo.${ClassName}BO;
#if($table.crud || $table.sub)
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;
#end
import java.util.Collection;

/**
 * ${functionName}Service业务层处理
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RequiredArgsConstructor
@Service
public class ${ClassName}ServiceImpl implements I${ClassName}Service {

    private final ${ClassName}Mapper baseMapper;

  #if($table.crud || $table.sub)
    /**
     * 查询${functionName}列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link ${ClassName}VO }>
     */
    @Override
    public TableDataInfo<${ClassName}VO> queryPageList(${ClassName}BO bo, PageQuery pageQuery){
        IPage<${ClassName}VO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<${ClassName}DO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getId()), ${ClassName}DO::getId, bo.getId())
            .eq(StrUtil.isNotEmpty(bo.getId()), ${ClassName}DO::getId, bo.getId())
        );
        List<${ClassName}VO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new TableDataInfo<>();
        }
        records.forEach(record -> {
        });
        return TableDataInfo.build(page);
    }
  #end

    /**
     * 查询${functionName}
     *
     * @param id id
     * @return {@link ${ClassName}VO }
     */
    @Override
    public ${ClassName}VO queryById(${pkColumn.javaType} ${pkColumn.javaField}){
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增${functionName}
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean insertByBo(${ClassName}BO bo){
        ${ClassName}DO ${className}DO = new ${ClassName}DO();
        BeanCopyUtils.copy(bo,${className}DO);
        ${className}DO.create();
        ${className}DO.update();
        return baseMapper.insert(${className}DO) > 0;
    }

    /**
     * 修改${functionName}
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(${ClassName}BO bo){
        ${ClassName}DO ${className}DO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(${className}DO)) {
            return false;
        }
        BeanCopyUtils.copy(bo,${className}DO);
        ${className}DO.update();
        return baseMapper.updateById(${className}DO) > 0;
    }

    /**
     * 校验并批量删除${functionName}信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<${pkColumn.javaType}> ids, Boolean isValid){
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
