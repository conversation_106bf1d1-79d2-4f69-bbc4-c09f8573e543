package com.jianyou.framework.config;

import com.influxdb.LogLevel;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.InfluxDBClientOptions;
import com.jianyou.framework.config.properties.InfluxdbProperties;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 时序数据库配置
 *
 * <AUTHOR>
 * @date 2023/12/20
 */
@Configuration
public class InfluxdbConfig {

    @Bean
    public InfluxDBClient influxDbClient(InfluxdbProperties influxdbProperties) {
        // 配置 InfluxDB 客户端选项，包括连接超时和请求超时
        InfluxDBClientOptions options = InfluxDBClientOptions.builder()
            .url(influxdbProperties.getUrl())
            .authenticateToken(influxdbProperties.getToken().toCharArray())
            .org(influxdbProperties.getOrg())
            .bucket(influxdbProperties.getBucket())
            .okHttpClient(new OkHttpClient.Builder().connectTimeout(Duration.ofSeconds(15)).readTimeout(20, TimeUnit.SECONDS))
            .build();
        // 创建 InfluxDBClient 实例
        InfluxDBClient influxDbClient = InfluxDBClientFactory.create(options);
        // 日志级别
        influxDbClient.setLogLevel(LogLevel.BASIC);
        return influxDbClient;
    }
}
