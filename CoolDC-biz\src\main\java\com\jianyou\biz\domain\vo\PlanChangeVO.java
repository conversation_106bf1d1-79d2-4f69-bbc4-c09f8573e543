package com.jianyou.biz.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jianyou.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 计划变更视图对象 biz_plan_change
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
public class PlanChangeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 施工计划id
     */
    @ExcelProperty(value = "施工计划id")
    private String buildPlanId;
    private String buildPlanName;

    /**
     * 变更事由
     */
    @ExcelProperty(value = "变更事由")
    private String changeReason;

    /**
     * 变更单（图片）
     */
    @ExcelProperty(value = "变更单", converter = ExcelDictConvert.class)
    private String ossIds;

    /**
     * 变更状态（未确认、确认、否认、废除）
     */
    @ExcelProperty(value = "变更状态", converter = ExcelDictConvert.class)
    private String changeStatus;

    /**
     * 变更时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "变更时间")
    private Date changeTime;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private String projectId;


}
