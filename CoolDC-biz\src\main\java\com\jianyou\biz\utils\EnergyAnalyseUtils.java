package com.jianyou.biz.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.MeterEnergyDO;
import com.jianyou.biz.domain.vo.EnergyAnalyseVO;
import com.jianyou.biz.mapper.MeterEnergyMapper;
import com.jianyou.common.enums.AnalysisType;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentSkipListMap;

/**
 * 能耗分析utils
 *
 * <AUTHOR>
 * @date 2025/07/05
 */
public class EnergyAnalyseUtils {

    /**
     * 从数据库汇总能耗数据
     *
     * @param meterEnergyMapper 能耗数据映射器
     * @param meterNumbers      表具编号列表
     * @param analysisType      分析类型
     * @param range             时间范围
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    public static Map<String, Object> collectEnergyFromDB(MeterEnergyMapper meterEnergyMapper,
                                                          List<String> meterNumbers,
                                                          AnalysisType analysisType,
                                                          Date[] range) {
        Map<String, Object> sumMap = new ConcurrentSkipListMap<>();

        // 查询指定时间范围内的能耗数据
        List<MeterEnergyDO> energyList = meterEnergyMapper.selectList(Wrappers.<MeterEnergyDO>lambdaQuery()
            .in(MeterEnergyDO::getMeterNumber, meterNumbers)
            .ge(MeterEnergyDO::getHour, range[0])
            .le(MeterEnergyDO::getHour, range[1])
            .orderBy(true, true, MeterEnergyDO::getHour)
        );

        if (CollUtil.isEmpty(energyList)) {
            return sumMap;
        }

        // 生成时间范围列表，与InfluxEnergyAnalyzer保持一致
        List<Date> timePoints = generateTimePoints(range[0], range[1], analysisType);

        // 为每个时间点计算能耗
        for (Date timePoint : timePoints) {
            String timeKey = formatTimeKey(timePoint, analysisType);

            // 计算该时间点的总能耗（所有表具的能耗之和）
            BigDecimal totalVolume = energyList.stream()
                .filter(energy -> isSameTimePeriod(energy.getHour(), timePoint, analysisType))
                .map(MeterEnergyDO::getVolume)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            sumMap.put(timeKey, totalVolume);
        }

        return sumMap;
    }

    /**
     * 从数据库查询单个表具的能耗数据
     *
     * @param meterEnergyMapper 能耗数据映射器
     * @param meterNumber       表具编号
     * @param analysisType      分析类型
     * @param range             时间范围
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    public static Map<String, Object> collectSingleMeterEnergyFromDB(MeterEnergyMapper meterEnergyMapper,
                                                                     String meterNumber,
                                                                     AnalysisType analysisType,
                                                                     Date[] range) {
        return collectEnergyFromDB(meterEnergyMapper, Collections.singletonList(meterNumber), analysisType, range);
    }

    /**
     * 计算总能耗
     *
     * @param meterEnergyMapper 能耗数据映射器
     * @param meterNumbers      表具编号列表
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @return {@link BigDecimal}
     */
    public static BigDecimal calculateTotalEnergyFromDB(MeterEnergyMapper meterEnergyMapper,
                                                        List<String> meterNumbers,
                                                        Date startTime,
                                                        Date endTime) {
        List<MeterEnergyDO> energyList = meterEnergyMapper.selectList(Wrappers.<MeterEnergyDO>lambdaQuery()
            .in(MeterEnergyDO::getMeterNumber, meterNumbers)
            .ge(MeterEnergyDO::getHour, startTime)
            .le(MeterEnergyDO::getHour, endTime)
        );

        if (CollUtil.isEmpty(energyList)) {
            return BigDecimal.ZERO;
        }

        return energyList.stream()
            .map(MeterEnergyDO::getVolume)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 根据分析类型格式化时间键
     *
     * @param date         日期
     * @param analysisType 分析类型
     * @return {@link String}
     */
    public static String formatTimeKey(Date date, AnalysisType analysisType) {
        // 使用与InfluxEnergyAnalyzer相同的格式化方式
        if (AnalysisType.WEEK.getValue().equals(analysisType.getValue())) {
            // 周分析特殊处理：生成"MM-dd至MM-dd"格式
            String begTime = DateUtil.format(date, analysisType.getFormat());
            String stopTime = DateUtil.format(DateUtil.offset(date, cn.hutool.core.date.DateField.of(analysisType.getDateField()), 1), analysisType.getFormat());
            return begTime + "至" + stopTime;
        } else {
            return DateUtil.format(date, analysisType.getFormat());
        }
    }

    /**
     * 生成时间点列表
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param analysisType 分析类型
     * @return {@link List}<{@link Date}>
     */
    public static List<Date> generateTimePoints(Date startTime, Date endTime, AnalysisType analysisType) {
        List<Date> timePoints = new ArrayList<>();

        switch (analysisType) {
            case YEARLY:
                Date yearStart = DateUtil.beginOfYear(startTime);
                Date yearEnd = DateUtil.beginOfYear(endTime);
                while (!yearStart.after(yearEnd)) {
                    timePoints.add(new Date(yearStart.getTime()));
                    yearStart = DateUtil.offset(yearStart, cn.hutool.core.date.DateField.YEAR, 1);
                }
                break;
            case MONTHLY:
                Date monthStart = DateUtil.beginOfMonth(startTime);
                Date monthEnd = DateUtil.beginOfMonth(endTime);
                while (!monthStart.after(monthEnd)) {
                    timePoints.add(new Date(monthStart.getTime()));
                    monthStart = DateUtil.offset(monthStart, cn.hutool.core.date.DateField.MONTH, 1);
                }
                break;
            case WEEK:
                Date weekStart = DateUtil.beginOfWeek(startTime);
                Date weekEnd = DateUtil.beginOfWeek(endTime);
                while (!weekStart.after(weekEnd)) {
                    timePoints.add(new Date(weekStart.getTime()));
                    weekStart = DateUtil.offset(weekStart, cn.hutool.core.date.DateField.WEEK_OF_YEAR, 1);
                }
                break;
            case DAILY:
                Date dayStart = DateUtil.beginOfDay(startTime);
                Date dayEnd = DateUtil.beginOfDay(endTime);
                while (!dayStart.after(dayEnd)) {
                    timePoints.add(new Date(dayStart.getTime()));
                    dayStart = DateUtil.offset(dayStart, cn.hutool.core.date.DateField.DAY_OF_YEAR, 1);
                }
                break;
            case HOURLY:
                Date hourStart = DateUtil.beginOfHour(startTime);
                Date hourEnd = DateUtil.beginOfHour(endTime);
                while (!hourStart.after(hourEnd)) {
                    timePoints.add(new Date(hourStart.getTime()));
                    hourStart = DateUtil.offset(hourStart, cn.hutool.core.date.DateField.HOUR_OF_DAY, 1);
                }
                break;
            default:
                break;
        }

        return timePoints;
    }

    /**
     * 判断两个时间是否属于同一个时间段
     *
     * @param date1        时间1
     * @param date2        时间2
     * @param analysisType 分析类型
     * @return boolean
     */
    public static boolean isSameTimePeriod(Date date1, Date date2, AnalysisType analysisType) {
        switch (analysisType) {
            case YEARLY:
                return DateUtil.year(date1) == DateUtil.year(date2);
            case MONTHLY:
                return DateUtil.year(date1) == DateUtil.year(date2) && DateUtil.month(date1) == DateUtil.month(date2);
            case WEEK:
                // 判断是否在同一周
                Date beginOfWeek1 = DateUtil.beginOfWeek(date1);
                Date beginOfWeek2 = DateUtil.beginOfWeek(date2);
                return beginOfWeek1.getTime() == beginOfWeek2.getTime();
            case DAILY:
                return DateUtil.isSameDay(date1, date2);
            case HOURLY:
                return DateUtil.isSameDay(date1, date2) && DateUtil.hour(date1, true) == DateUtil.hour(date2, true);
            default:
                return false;
        }
    }

    public static void buildTimeEnergy(Map<String, Object> chartsData, EnergyAnalyseVO energyAnalyseVO) {
        LinkedList<String> times = new LinkedList<>();
        LinkedList<Double> thisEnergy = new LinkedList<>();
        LinkedList<Double> qoqEnergy = new LinkedList<>();
        chartsData.forEach((k, v) -> {
            times.add(k);
            thisEnergy.add(Convert.toDouble(v));
            qoqEnergy.add(Convert.toDouble(v));
        });
        times.removeFirst();
        thisEnergy.removeFirst();
        qoqEnergy.removeLast();
        energyAnalyseVO.setTimes(times);
        energyAnalyseVO.setThisEnergy(thisEnergy);
        energyAnalyseVO.setQoqEnergy(qoqEnergy);
    }

}
