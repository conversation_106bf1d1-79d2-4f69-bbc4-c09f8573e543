package com.jianyou.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.FacilityGetReadingDO;
import com.jianyou.biz.domain.FacilityGetStorageDO;
import com.jianyou.biz.domain.FacilityListDO;
import com.jianyou.biz.domain.bo.FacilityGetReadingBO;
import com.jianyou.biz.mapper.FacilityGetReadingMapper;
import com.jianyou.biz.mapper.FacilityListMapper;
import com.jianyou.biz.service.IFacilityStorageService;
import com.jianyou.common.exception.ServiceException;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.jianyou.biz.mapper.FacilityGetStorageMapper;
import com.jianyou.biz.service.IFacilityGetStorageService;
import com.jianyou.biz.domain.vo.FacilityGetStorageVO;
import com.jianyou.biz.domain.bo.FacilityGetStorageBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备请领Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@RequiredArgsConstructor
@Service
public class FacilityGetStorageServiceImpl implements IFacilityGetStorageService {

    private final FacilityGetStorageMapper baseMapper;
    private final FacilityGetReadingMapper facilityGetReadingMapper;
    private final IFacilityStorageService facilityStorageService;
    private final FacilityListMapper facilityListMapper;

    /**
     * 查询设备请领列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link FacilityGetStorageVO }>
     */
    @Override
    public TableDataInfo<FacilityGetStorageVO> queryPageList(FacilityGetStorageBO bo, PageQuery pageQuery) {
        IPage<FacilityGetStorageVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<FacilityGetStorageDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getGetPrincipal()), FacilityGetStorageDO::getGetPrincipal, bo.getGetPrincipal())
            .eq(StrUtil.isNotEmpty(bo.getProjectId()), FacilityGetStorageDO::getProjectId, bo.getProjectId())
            .orderByDesc(FacilityGetStorageDO::getDrawTime)
        );
        return TableDataInfo.build(page);
    }

    /**
     * 查询设备请领
     *
     * @param id id
     * @return {@link FacilityGetStorageVO }
     */
    @Override
    public FacilityGetStorageVO queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增设备请领
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(FacilityGetStorageBO bo) {
        FacilityGetStorageDO facilityGetStorageDO = new FacilityGetStorageDO();
        BeanCopyUtils.copy(bo, facilityGetStorageDO);
        facilityGetStorageDO.create();
        facilityGetStorageDO.update();
        baseMapper.insert(facilityGetStorageDO);
        String facilityGetStorageId = facilityGetStorageDO.getId();

        List<FacilityGetReadingBO> facilityDetails = bo.getFacilityDetails();
        List<String> facilityIds = facilityDetails.stream().map(FacilityGetReadingBO::getFacilityId).collect(Collectors.toList());
        // 获取设备信息
        Map<String, FacilityListDO> map = facilityListMapper.nameDict(facilityIds);

        // 保存设备领取记录
        List<FacilityGetReadingDO> list = facilityDetails.stream().map(item -> {
            String facilityId = item.getFacilityId();
            BigDecimal count = item.getCount();
            if (ObjUtil.isEmpty(count)) {
                throw new ServiceException("请领数量不能为空");
            }
            if (count.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("请领数量不能小于0");
            }
            FacilityGetReadingDO copy = BeanUtil.copyProperties(item, FacilityGetReadingDO.class);
            copy.setFacilityGetStorageId(facilityGetStorageId);
            FacilityListDO facility = map.get(facilityId);
            String facilityName = "";
            if (facility != null) {
                facilityName = facility.getName();
                copy.setFacilityName(facilityName);
                copy.setFactoryName(facility.getFactoryName());
                copy.setFacilityModel(facility.getModel());
            }
            // 更新库存
            Boolean stock = facilityStorageService.updateStock(facilityId, bo.getProjectId(), count.negate(), facilityName);
            if (!stock) {
                throw new ServiceException("更新库存失败");
            }
            return copy;
        }).collect(Collectors.toList());
        facilityGetReadingMapper.insertBatch(list);
        return true;
    }

    /**
     * 修改设备请领
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(FacilityGetStorageBO bo) {
        FacilityGetStorageDO facilityGetStorageDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(facilityGetStorageDO)) {
            return false;
        }
        BeanCopyUtils.copy(bo, facilityGetStorageDO);
        facilityGetStorageDO.update();
        return baseMapper.updateById(facilityGetStorageDO) > 0;
    }

    /**
     * 校验并批量删除设备请领信息
     *
     * @param ids     主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
