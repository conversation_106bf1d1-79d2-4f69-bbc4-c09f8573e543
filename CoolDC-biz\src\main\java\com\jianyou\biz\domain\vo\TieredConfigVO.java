package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 阶梯配置视图对象 enms_tiered_config
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@ExcelIgnoreUnannotated
public class TieredConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 计费配置ID
     */
    @ExcelProperty(value = "计费配置ID")
    private String billingConfigId;

    /**
     * 阶段
     */
    @ExcelProperty(value = "阶段")
    private Long stage;

    /**
     * 费率
     */
    @ExcelProperty(value = "费率")
    private BigDecimal rate;

    /**
     * 起始能耗
     */
    @ExcelProperty(value = "起始能耗")
    private BigDecimal startEnergy;

    /**
     * 截止能耗
     */
    @ExcelProperty(value = "截止能耗")
    private BigDecimal endEnergy;


}
