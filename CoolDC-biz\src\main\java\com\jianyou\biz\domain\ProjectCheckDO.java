package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.jianyou.common.core.domain.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 项目验收对象 biz_project_check
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_project_check")
public class ProjectCheckDO extends BaseDO {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private String id;
    /**
     * 验收报告（上传附件）
     */
    private String reportOssIds;
    /**
     * 验收资料（图纸、单据）
     */
    private String dataOssIds;
    /**
     * 验收人
     */
    private String acceptor;
    /**
     * 验收时间
     */
    private Date receiveTime;
    /**
     * 项目id
     */
    private String projectId;

}
