package com.jianyou.web.controller.biz;


import com.jianyou.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.biz.domain.vo.FacilityGetReadingVO;
import com.jianyou.biz.domain.bo.FacilityGetReadingBO;
import com.jianyou.biz.service.IFacilityGetReadingService;

import java.util.List;

/**
 * 设备请领记录
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/facilityGetReading")
public class FacilityGetReadingController extends BaseController {

    private final IFacilityGetReadingService facilityGetReadingService;

    /**
     * 查询设备请领记录列表
     */
    @GetMapping("/list")
    public R<List<FacilityGetReadingVO>> list(FacilityGetReadingBO bo) {
        return R.ok(facilityGetReadingService.queryList(bo));
    }

}
