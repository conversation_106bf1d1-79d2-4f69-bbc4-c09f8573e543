package com.jianyou.common.utils.influxdb.domain.pojo;

import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * influxdb表具能耗用量对象
 *
 * <AUTHOR>
 * @date 2024/11/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Measurement(name = "meterVolume")
public class MeterVolumePojo extends BasePojo {

    /** 表具能耗用量 用于分析 */
    @Column
    private Double volume;

}
