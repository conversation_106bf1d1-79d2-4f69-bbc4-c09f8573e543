package com.jianyou.biz.utils;


import com.jianyou.biz.domain.dto.*;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 电表计费计算器utils
 *
 * <AUTHOR>
 * @date 2025/07/14
 */
public class MeterBillingCalculatorUtils {

    /**
     * 计费
     *
     * @param readings                电表读数列表（必须按时间升序）
     * @param ladders                 阶梯配置列表（按阶梯顺序排序）
     * @param timePrices              峰平谷时间段价格
     * @param standardBasePrice       标准计价基础单价
     * @param config                  计费配置
     * @param initialCumulativeVolume 初始累计用量
     * @return {@link BillingResult} 包含明细、总费用、最终累计量
     */
    public static BillingResult calculate(List<MeterReading> readings,
                                          List<Ladder> ladders,
                                          List<TimePrice> timePrices,
                                          BigDecimal standardBasePrice,
                                          BillingConfig config,
                                          BigDecimal initialCumulativeVolume) {

        BigDecimal totalFee = BigDecimal.ZERO;
        BigDecimal totalVolume = BigDecimal.ZERO;
        BigDecimal cumulativeVolume = initialCumulativeVolume != null ? initialCumulativeVolume : BigDecimal.ZERO;

        Map<String, ChargeDetail> detailMap = new LinkedHashMap<>();

        for (int i = 1; i < readings.size(); i++) {
            MeterReading prev = readings.get(i - 1);
            MeterReading curr = readings.get(i);

            BigDecimal segmentVolume = curr.getValue().subtract(prev.getValue());
            if (segmentVolume.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            totalVolume = totalVolume.add(segmentVolume);

            LocalTime time = curr.getTime().toLocalTime();
            String timeName = "平";
            String timeRange = "00:00–24:00";
            BigDecimal addPrice = BigDecimal.ZERO;

            // 峰平谷判断
            if ("1".equals(config.getPeakValleyFlag())) {
                TimePrice tp = findTimePrice(time, timePrices);
                if (tp != null) {
                    timeName = tp.getName();
                    timeRange = tp.getStart().toString() + "–" + tp.getEnd().toString();
                    addPrice = tp.getPrice();
                }
            }

            if ("0".equals(config.getRateType())) {
                // 标准计费
                BigDecimal unitPrice = standardBasePrice.add(addPrice);
                BigDecimal fee = segmentVolume.multiply(unitPrice);

                String key = "标准|" + timeName + "|" + timeRange;
                ChargeDetail d = detailMap.get(key);
                if (d == null) {
                    d = new ChargeDetail("标准", timeName, timeRange, segmentVolume,
                        standardBasePrice, addPrice, fee, null);
                    detailMap.put(key, d);
                } else {
                    d.setVolume(d.getVolume().add(segmentVolume));
                    d.setFee(d.getFee().add(fee));
                }
                totalFee = totalFee.add(fee);

            } else if ("1".equals(config.getRateType())) {
                // 阶梯计费
                BigDecimal remainVolume = segmentVolume;

                for (int j = 0; j < ladders.size(); j++) {
                    Ladder ladder = ladders.get(j);
                    boolean isLastLadder = (j == ladders.size() - 1);

                    // 最后一档 maxVolume == 0 表示“无限”
                    BigDecimal ladderRemain;
                    if (!isLastLadder || ladder.getMaxVolume() != null && ladder.getMaxVolume().compareTo(BigDecimal.ZERO) > 0) {
                        ladderRemain = ladder.getMaxVolume().subtract(cumulativeVolume);
                        if (ladderRemain.compareTo(BigDecimal.ZERO) <= 0) {
                            continue; // 当前档已满，跳过
                        }
                    } else {
                        // 最后一档无限
                        ladderRemain = remainVolume;
                    }

                    BigDecimal alloc = remainVolume.min(ladderRemain);
                    cumulativeVolume = cumulativeVolume.add(alloc);
                    remainVolume = remainVolume.subtract(alloc);

                    BigDecimal unitPrice = ladder.getPrice().add(addPrice);
                    BigDecimal fee = alloc.multiply(unitPrice);

                    String key = ladder.getName() + "|" + timeName + "|" + timeRange;
                    ChargeDetail d = detailMap.get(key);
                    if (d == null) {
                        d = new ChargeDetail(ladder.getName(), timeName, timeRange, alloc,
                            ladder.getPrice(), addPrice, fee,
                            ladder.getMaxVolume().compareTo(BigDecimal.ZERO) == 0 ? null : ladder.getMaxVolume());
                        detailMap.put(key, d);
                    } else {
                        d.setVolume(d.getVolume().add(alloc));
                        d.setFee(d.getFee().add(fee));
                    }

                    totalFee = totalFee.add(fee);

                    if (remainVolume.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                }
            }
        }

        List<ChargeDetail> details = new ArrayList<>(detailMap.values());
        return new BillingResult(details, totalFee, cumulativeVolume, totalVolume);
    }


    /**
     * 查找时间区间
     */
    private static TimePrice findTimePrice(LocalTime time, List<TimePrice> timePrices) {
        for (TimePrice tp : timePrices) {
            if (tp.getStart().isAfter(tp.getEnd())) {
                // 跨午夜
                if (!time.isBefore(tp.getStart()) || !time.isAfter(tp.getEnd())) {
                    return tp;
                }
            } else {
                if (!time.isBefore(tp.getStart()) && !time.isAfter(tp.getEnd())) {
                    return tp;
                }
            }
        }
        return null;
    }
}
