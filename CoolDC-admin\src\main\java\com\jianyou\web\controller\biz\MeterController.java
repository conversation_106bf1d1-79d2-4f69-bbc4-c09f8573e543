package com.jianyou.web.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import com.jianyou.biz.domain.bo.FeeBO;
import com.jianyou.biz.domain.bo.MeterBO;
import com.jianyou.biz.domain.vo.MeterVO;
import com.jianyou.biz.service.IMeterService;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 表具
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/meter")
public class MeterController extends BaseController {

    private final IMeterService meterService;

    /**
     * 查询表具列表
     */
    @SaCheckPermission("biz:meter:list")
    @GetMapping("/list")
    public TableDataInfo<MeterVO> list(MeterBO bo, PageQuery pageQuery) {
        return meterService.queryPageList(bo, pageQuery, true);
    }

    /**
     * 导出表具列表
     */
    @SaCheckPermission("biz:meter:export")
    @PostMapping("/export")
    public void export(MeterBO bo, HttpServletResponse response) {
        TableDataInfo<MeterVO> tableDataInfo = meterService.queryPageList(bo, null, false);
        ExcelUtil.exportExcel(tableDataInfo.getRows(), "表具", MeterVO.class, response);
    }

    /**
     * 获取表具详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:meter:query")
    @GetMapping("/{id}")
    public R<MeterVO> getInfo(@NotNull(message = "主键不能为空")
                              @PathVariable String id) {
        return R.ok(meterService.queryById(id));
    }

    /**
     * 新增表具
     */
    @SaCheckPermission("biz:meter:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MeterBO bo) {
        return toAjax(meterService.insertByBo(bo));
    }

    /**
     * 修改表具
     */
    @SaCheckPermission("biz:meter:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MeterBO bo) {
        return toAjax(meterService.updateByBo(bo));
    }

    /**
     * 删除表具
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:meter:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(meterService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 获取区域表具树结构
     */
    @GetMapping("/meterTree")
    public R<List<Tree<String>>> meterTree(String energyType) {
        return R.ok(meterService.queryAreaMeterTree(energyType));
    }

    /**
     * 预存
     */
    @SaCheckPermission("biz:meter:topUp")
    @RepeatSubmit()
    @PostMapping("/topUp")
    public R<Void> topUp(@Validated(AddGroup.class) @RequestBody FeeBO bo) {
        return toAjax(meterService.topUp(bo));
    }

    /**
     * 退费
     */
    @SaCheckPermission("biz:meter:returnFee")
    @RepeatSubmit()
    @PostMapping("/returnFee")
    public R<Void> returnFee(@Validated(AddGroup.class) @RequestBody FeeBO bo) {
        return toAjax(meterService.returnFee(bo));
    }

    /**
     * 修改自动退费
     */
    @SaCheckPermission("biz:meter:edit")
    @RepeatSubmit()
    @PutMapping("/editAutoPay")
    public R<Void> editAutoPay(@Validated(EditGroup.class) @RequestBody FeeBO bo) {
        return toAjax(meterService.editAutoPay(bo));
    }

    /**
     * 抄表
     */
    @SaCheckPermission("biz:meter:reading")
    @RepeatSubmit()
    @PostMapping("/reading/{meterIds}")
    public R<Void> reading(@NotEmpty(message = "表具不能为空")
                           @PathVariable String[] meterIds) {
        return toAjax(meterService.reading(Arrays.asList(meterIds), "1", true));
    }

}
