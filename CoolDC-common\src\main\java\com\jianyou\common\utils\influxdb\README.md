# InfluxDB 时间开窗聚合工具

## 功能概述

新增了 `selectWindowAggregation` 方法，支持对InfluxDB数据进行时间开窗聚合查询，具有以下特性：

- ✅ **时区支持**: 自动设置为上海时区（UTC+8），从0点开始计算窗口
- ✅ **多维度开窗**: 支持按多个标签维度进行分组聚合
- ✅ **字段聚合**: 对指定字段进行sum聚合计算
- ✅ **灵活窗口**: 支持多种时间窗口大小（分钟、小时、天等）

## 方法签名

```java
public List<WindowAggregationResult> selectWindowAggregation(
    @NotNull InfluxdbMeasurement measurement,    // 测量表
    List<String> tags,                           // 标签列表（可为空查询所有）
    @NotNull InfluxdbField field,                // 需要聚合的字段
    @NotNull Date startTime,                     // 开始时间
    @NotNull Date endTime,                       // 结束时间
    @NotNull String windowDuration,              // 时间窗口大小
    List<String> groupByTags                     // 分组维度标签（可为空）
)
```

## 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| measurement | InfluxdbMeasurement | ✅ | 要查询的测量表 |
| tags | List<String> | ❌ | 设备标签列表，为空则查询所有设备 |
| field | InfluxdbField | ✅ | 要聚合的字段名 |
| startTime | Date | ✅ | 查询开始时间 |
| endTime | Date | ✅ | 查询结束时间 |
| windowDuration | String | ✅ | 时间窗口大小，如："1h", "1d", "15m" |
| groupByTags | List<String> | ❌ | 额外的分组维度标签 |

## 时间窗口格式

支持的时间窗口格式：

- `15m` - 15分钟
- `1h` - 1小时
- `6h` - 6小时
- `1d` - 1天
- `1w` - 1周
- `1mo` - 1个月

## 返回结果

`WindowAggregationResult` 对象包含：

```java
public class WindowAggregationResult {
    private String tag;                    // 设备标签
    private String field;                  // 字段名
    private Double aggregatedValue;        // 聚合值
    private String windowStart;            // 窗口开始时间
    private String windowStop;             // 窗口结束时间
    private Map<String, String> dimensions; // 其他维度标签
}
```

## 使用示例

### 示例1：按小时聚合单个设备

```java
@Autowired
private InfluxUtils influxUtils;

public void hourlyAggregation() {
    List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
        InfluxdbMeasurement.METER,           // 表具测量
        Arrays.asList("device001"),          // 单个设备
        InfluxdbField.READING,               // 读数字段
        DateUtil.offsetHour(new Date(), -24), // 24小时前
        new Date(),                          // 现在
        "1h",                               // 1小时窗口
        null                                // 无额外分组
    );
    
    // 处理结果
    results.forEach(result -> {
        System.out.printf("设备: %s, 时间: %s, 小时聚合值: %.2f%n",
            result.getTag(),
            result.getWindowStart(),
            result.getAggregatedValue()
        );
    });
}
```

### 示例2：多设备按天聚合，按区域分组

```java
public void dailyAggregationWithRegionGrouping() {
    List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
        InfluxdbMeasurement.METER,
        Arrays.asList("device001", "device002", "device003"),
        InfluxdbField.READING,
        DateUtil.offsetDay(new Date(), -7),  // 7天前
        new Date(),
        "1d",                               // 1天窗口
        Arrays.asList("region", "building")  // 按区域和建筑分组
    );
    
    // 按维度分组处理结果
    Map<String, List<WindowAggregationResult>> groupedResults = results.stream()
        .collect(Collectors.groupingBy(
            result -> result.getDimensions().toString()
        ));
        
    groupedResults.forEach((dimensions, groupResults) -> {
        System.out.println("维度组合: " + dimensions);
        groupResults.forEach(result -> {
            System.out.printf("  设备: %s, 日期: %s, 日聚合值: %.2f%n",
                result.getTag(),
                result.getWindowStart(),
                result.getAggregatedValue()
            );
        });
    });
}
```

### 示例3：15分钟窗口实时监控

```java
public void realTimeMonitoring() {
    List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
        InfluxdbMeasurement.METER,
        null,                               // 所有设备
        InfluxdbField.READING,
        DateUtil.offsetHour(new Date(), -2), // 2小时前
        new Date(),
        "15m",                              // 15分钟窗口
        null
    );
    
    // 实时监控处理
    results.forEach(result -> {
        if (result.getAggregatedValue() > 1000) { // 阈值检查
            System.out.printf("⚠️ 设备 %s 在 %s 的15分钟聚合值异常: %.2f%n",
                result.getTag(),
                result.getWindowStart(),
                result.getAggregatedValue()
            );
        }
    });
}
```

## 注意事项

1. **时区处理**: 所有时间窗口都基于上海时区（UTC+8）计算，确保从0点开始对齐
2. **性能考虑**: 大时间范围查询建议使用较大的时间窗口以提高性能
3. **数据精度**: 聚合结果为Double类型，适合数值型字段的sum操作
4. **空值处理**: 如果某个时间窗口内没有数据，该窗口不会出现在结果中
5. **标签过滤**: tags参数为空时会查询所有设备，请根据实际需求谨慎使用

## 生成的Flux查询示例

```flux
from(bucket: "your-bucket")
  |> range(start: 2025-01-01T00:00:00Z, stop: 2025-01-02T00:00:00Z)
  |> filter(fn: (r) => r["_measurement"] == "meter")
  |> filter(fn: (r) => r["_field"] == "reading")
  |> filter(fn: (r) => contains(value: r["tagId"], set: ["device001", "device002"]))
  |> aggregateWindow(
    every: 1h,
    fn: sum,
    createEmpty: false,
    timeSrc: "_start",
    offset: 8h
  )
  |> group(columns: ["_measurement", "_field", "tagId", "region"])
  |> sort(columns: ["_time"])
```
