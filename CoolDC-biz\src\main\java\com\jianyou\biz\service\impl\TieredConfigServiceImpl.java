package com.jianyou.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.TieredConfigDO;
import com.jianyou.biz.domain.bo.TieredConfigBO;
import com.jianyou.biz.domain.vo.TieredConfigVO;
import com.jianyou.biz.mapper.TieredConfigMapper;
import com.jianyou.biz.service.ITieredConfigService;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 阶梯配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RequiredArgsConstructor
@Service
public class TieredConfigServiceImpl implements ITieredConfigService {

    private final TieredConfigMapper baseMapper;

    /**
     * 根据计费配置ID查询阶梯配置列表
     *
     * @param billingConfigId 计费配置ID
     * @return 阶梯配置列表
     */
    @Override
    public List<TieredConfigVO> queryByBillingConfigId(String billingConfigId) {
        LambdaQueryWrapper<TieredConfigDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TieredConfigDO::getBillingConfigId, billingConfigId);
        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 批量新增阶梯配置
     *
     * @param tieredConfigs 阶梯配置列表
     * @return 结果
     */
    @Override
    public boolean insertBatch(List<TieredConfigBO> tieredConfigs) {
        if (tieredConfigs == null || tieredConfigs.isEmpty()) {
            return true;
        }

        List<TieredConfigDO> tieredConfigDOList = new ArrayList<>();
        for (TieredConfigBO tieredConfigBO : tieredConfigs) {
            TieredConfigDO tieredConfigDO = new TieredConfigDO();
            BeanCopyUtils.copy(tieredConfigBO, tieredConfigDO);
            tieredConfigDOList.add(tieredConfigDO);
        }

        return baseMapper.insertBatch(tieredConfigDOList);
    }

    /**
     * 根据计费配置ID删除阶梯配置
     *
     * @param billingConfigId 计费配置ID
     * @return 结果
     */
    @Override
    public boolean deleteByBillingConfigId(String billingConfigId) {
        LambdaQueryWrapper<TieredConfigDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TieredConfigDO::getBillingConfigId, billingConfigId);
        return baseMapper.delete(wrapper) >= 0;
    }
}
