package com.jianyou.biz.service;

import com.jianyou.biz.domain.bo.GroupBO;
import com.jianyou.biz.domain.vo.GroupVO;
import com.jianyou.common.core.domain.BaseOptionsVO;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 表具分组Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IGroupService {


    /**
     * 查询表具分组列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link GroupVO }>
     */
    TableDataInfo<GroupVO> queryPageList(GroupBO bo, PageQuery pageQuery);

    /**
     * 查询表具分组
     *
     * @param id id
     * @return {@link GroupVO }
     */
     GroupVO queryById(String id);

    /**
     * 新增表具分组
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(GroupBO bo);

    /**
     * 修改表具分组
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(GroupBO bo);

    /**
     * 校验并批量删除表具分组信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 查询表具分组选项
     */
    List<BaseOptionsVO> option();
}
