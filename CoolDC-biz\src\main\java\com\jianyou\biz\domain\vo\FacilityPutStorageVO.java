package com.jianyou.biz.domain.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备入库记录视图对象 biz_facility_put_storage
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@ExcelIgnoreUnannotated
public class FacilityPutStorageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 设备id
     */
    private String facilityId;

    /**
     * 设备名称
     */
    private String facilityName;

    /**
     * 入库数量
     */
    @ExcelProperty(value = "入库数量")
    private BigDecimal count;

    /**
     * 入库时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "入库时间")
    private Date entryTime;
    /**
     * 项目id
     */
    private String projectId;


}
