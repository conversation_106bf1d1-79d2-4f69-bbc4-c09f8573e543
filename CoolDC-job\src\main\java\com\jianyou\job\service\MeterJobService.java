package com.jianyou.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.MeterDO;
import com.jianyou.biz.domain.MeterEnergyDO;
import com.jianyou.biz.mapper.MeterEnergyMapper;
import com.jianyou.biz.mapper.MeterMapper;
import com.jianyou.biz.service.IMeterService;
import com.jianyou.common.enums.AnalysisType;
import com.jianyou.common.enums.InfluxdbField;
import com.jianyou.common.enums.InfluxdbMeasurement;
import com.jianyou.common.utils.TimeFormatUtil;
import com.jianyou.common.utils.influxdb.InfluxEnergyAnalyzer;
import com.jianyou.common.utils.influxdb.InfluxUtils;
import com.jianyou.common.utils.influxdb.domain.dto.InfluxRes;
import com.jianyou.common.utils.influxdb.domain.pojo.MeterVolumePojo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 表具定时任务服务
 *
 * <AUTHOR>
 * @date 2025/07/19
 */
@Service
@RequiredArgsConstructor
public class MeterJobService {

    private final IMeterService meterService;
    private final MeterMapper meterMapper;
    private final InfluxUtils influxUtils;
    private final MeterEnergyMapper meterEnergyMapper;

    /**
     * 抄表
     */
    @XxlJob("meterReadingHandler")
    public void meterReadingHandler() throws Exception {
        List<MeterDO> meters = meterMapper.selectList(Wrappers.<MeterDO>lambdaQuery()
            .select(MeterDO::getId)
        );
        if (CollUtil.isEmpty(meters)) {
            return;
        }
        boolean isBilling = false;
        String command = XxlJobHelper.getJobParam();
        if (command != null) {
            isBilling = Convert.toBool(command);
        }
        List<String> meterIds = meters.stream().map(MeterDO::getId).collect(Collectors.toList());
        meterService.reading(meterIds, "0", isBilling);
    }

    /**
     * 同步读数
     */
    @XxlJob("syncReadingHandler")
    public void syncReadingHandler() throws Exception {
        List<MeterDO> meters = meterMapper.selectList();
        if (CollUtil.isEmpty(meters)) {
            return;
        }
        Date now = new Date();
        List<String> numbers = meters.stream().map(MeterDO::getNumber).collect(Collectors.toList());
        Map<String, List<InfluxRes>> resMap = influxUtils.selectBatchLatest(InfluxdbMeasurement.METER,
            numbers,
            Collections.singletonList(InfluxdbField.READING),
            null, now);
        if (CollUtil.isEmpty(resMap)) {
            return;
        }
        meters.forEach(meter -> {
            // 获取读数
            List<InfluxRes> res = resMap.get(meter.getNumber());
            if (res != null) {
                InfluxRes influxRes = res.get(0);
                BigDecimal meterRecord = new BigDecimal(influxRes.getValue());
                meter.setMeterRecord(meterRecord);
                meter.setMeterTime(influxRes.getTime());
                meter.setSyncTime(now);
            }
        });
        meterMapper.updateBatchById(meters);
    }

    /**
     * 分析能耗
     */
    @XxlJob("analyseEnergyHandler")
    public void analyseEnergyHandler() throws Exception {
        List<MeterDO> meters = meterMapper.selectList();
        if (CollUtil.isEmpty(meters)) {
            return;
        }

        String yesterday = "";
        String command = XxlJobHelper.getJobParam();
        if (StrUtil.isNotEmpty(command)) {
            yesterday = command;
        }else {
            // 计算昨天的日期
            yesterday = DateUtil.formatDateTime(DateUtil.offsetDay(new Date(),-1));
        }
        Date[] rangeOpen = TimeFormatUtil.formatRange(yesterday, yesterday, AnalysisType.DAILY, true);

        List<String> numbers = meters.stream().map(MeterDO::getNumber).collect(Collectors.toList());
        Map<String, List<InfluxRes>> resMap = influxUtils.selectBatch(InfluxdbMeasurement.METER,
            numbers,
            Collections.singletonList(InfluxdbField.READING),
            rangeOpen[0], rangeOpen[1], false, -3);
        if (CollUtil.isEmpty(resMap)) {
            return;
        }
        Date[] rangeClose = TimeFormatUtil.formatRange(yesterday, yesterday, AnalysisType.DAILY, false);
        List<MeterEnergyDO> list = new ArrayList<>();
        List<MeterVolumePojo> meterVolumePojoList = new ArrayList<>();
        meters.forEach(meter -> {
            String number = meter.getNumber();
            List<InfluxRes> res = resMap.get(number);
            if (res == null){
                return;
            }
            Map<String, Object> map = InfluxEnergyAnalyzer.energyAnalyze(res, AnalysisType.HOURLY, rangeClose[0], rangeClose[1], false);

            map.forEach((k, v) -> {
                MeterEnergyDO meterEnergyDO = new MeterEnergyDO();
                meterEnergyDO.setMeterNumber(number);
                meterEnergyDO.setCreateTime(new Date());
                meterEnergyDO.setVolume(new BigDecimal(v.toString()));
                meterEnergyDO.setHour(DateUtil.parseDateTime(k));
                list.add(meterEnergyDO);

                // 能耗使用量
                MeterVolumePojo meterVolumePojo = new MeterVolumePojo();
                meterVolumePojo.setTagId( number);
                meterVolumePojo.setTime(DateUtil.parseDateTime(k).toInstant());
                meterVolumePojo.setVolume(new BigDecimal(v.toString()).doubleValue());
                meterVolumePojoList.add(meterVolumePojo);
            });
        });
        // 先删除旧数据
        meterEnergyMapper.delete(Wrappers.<MeterEnergyDO>lambdaQuery()
            .in(MeterEnergyDO::getMeterNumber,numbers)
            .between(MeterEnergyDO::getHour, rangeClose[0], rangeClose[1])
        );
        meterEnergyMapper.insertBatch(list);

        // 保存到时序库
        influxUtils.insertPojo(meterVolumePojoList);
    }

}
