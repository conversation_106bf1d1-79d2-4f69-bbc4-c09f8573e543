package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.validate.QueryGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 表分析bo
 *
 * <AUTHOR>
 * @date 2025/07/03
 */
@Data
public class MeterAnalyseBO {

    @NotBlank(message = "表具不能为空", groups = { QueryGroup.class })
    private String meterId;
    @NotBlank(message = "分析方式不能为空", groups = { QueryGroup.class })
    private String analysisType;
    @NotBlank(message = "起始时间不能为空", groups = { QueryGroup.class })
    private String startTime;
    @NotBlank(message = "截止时间不能为空", groups = { QueryGroup.class })
    private String endTime;
    @NotBlank(message = "点位类型不能为空", groups = { QueryGroup.class })
    private String pointType;

}
