package com.jianyou.biz.domain.vo;

import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备请领记录视图对象 biz_facility_get_reading
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@ExcelIgnoreUnannotated
public class FacilityGetReadingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 设备请领id
     */
    @ExcelProperty(value = "设备请领id")
    private String facilityGetStorageId;

    /**
     * 设备id
     */
    @ExcelProperty(value = "设备id")
    private String facilityId;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String facilityName;

    /**
     * 规格型号
     */
    @ExcelProperty(value = "规格型号")
    private String facilityModel;

    /**
     * 厂家名称
     */
    @ExcelProperty(value = "厂家名称")
    private String factoryName;

    /**
     * 请领数量
     */
    @ExcelProperty(value = "请领数量")
    private BigDecimal count;


}
