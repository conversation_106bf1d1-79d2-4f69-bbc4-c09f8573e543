package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 具能耗用量视图对象 enms_meter_energy
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@ExcelIgnoreUnannotated
public class MeterEnergyVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 表具编号
     */
    @ExcelProperty(value = "表具编号")
    private String meterNumber;

    /**
     * 能耗用量
     */
    @ExcelProperty(value = "能耗用量")
    private BigDecimal volume;

    /**
     * 小时
     */
    @ExcelProperty(value = "小时")
    private Date hour;


}
