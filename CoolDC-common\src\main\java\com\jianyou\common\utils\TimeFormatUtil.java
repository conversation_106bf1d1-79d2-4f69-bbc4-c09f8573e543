package com.jianyou.common.utils;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.jianyou.common.enums.AnalysisType;
import com.jianyou.common.exception.ServiceException;

import java.util.Date;

/**
 * 时间格式工具
 *
 * <AUTHOR>
 * @date 2025/07/03
 */
public class TimeFormatUtil {


    /**
     * 根据分析方式格式化 startTime 和 endTime，
     * 支持控制是否使用开区间（end 为下一个单位起点）
     *
     * @param startTime       起始时间
     * @param endTime         截止时间
     * @param analysisType    分析方式
     * @param isOpenInterval  是否使用开区间：true = end 为下一单位起点，false = end 为当前单位末尾
     * @return 格式化后的起止时间数组 [start, end)
     */
    public static Date[] formatRange(String startTime, String endTime, AnalysisType analysisType, boolean isOpenInterval) {
        DateTime startDate = DateUtil.parseDateTime(startTime);
        DateTime endDate = DateUtil.parseDateTime(endTime);
        System.out.println(endDate);
        DateTime start, end;
        switch (analysisType) {
            case YEARLY:
                start = DateUtil.beginOfYear(startDate);
                end = isOpenInterval
                    ? DateUtil.beginOfYear(DateUtil.offset(endDate, DateField.YEAR, 1))
                    : DateUtil.endOfYear(endDate);
                break;
            case MONTHLY:
                start = DateUtil.beginOfMonth(startDate);
                end = isOpenInterval
                    ? DateUtil.beginOfMonth(DateUtil.offset(endDate, DateField.MONTH, 1))
                    : DateUtil.endOfMonth(endDate);
                break;
            case WEEK:
                start = DateUtil.beginOfWeek(startDate);
                end = isOpenInterval
                    ? DateUtil.beginOfWeek(DateUtil.offset(endDate, DateField.WEEK_OF_YEAR, 1))
                    : DateUtil.endOfWeek(endDate);
                break;
            case DAILY:
                start = DateUtil.beginOfDay(startDate);
                end = isOpenInterval
                    ? DateUtil.beginOfDay(DateUtil.offset(endDate, DateField.DAY_OF_YEAR, 1))
                    : DateUtil.endOfDay(endDate);
                break;
            case HOURLY:
                start = DateUtil.beginOfHour(startDate);
                end = isOpenInterval
                    ? DateUtil.beginOfHour(DateUtil.offset(endDate, DateField.HOUR_OF_DAY, 1))
                    : DateUtil.endOfHour(endDate);
                break;
            default:
                throw new ServiceException("暂不支持的分析方式: " + analysisType);
        }

        return new Date[]{start, end};
    }

    /**
     * 偏移时间
     *
     * @param time         时间
     * @param analysisType 分析方式
     * @param offset       偏移量
     * @return {@link String }
     */
    public static String offsetTime(String time, AnalysisType analysisType, int offset) {
        // 先按 format 解析
        DateTime date = DateUtil.parseDateTime(time);
        Date offsetDate;
        switch (analysisType) {
            case YEARLY:
                offsetDate = DateUtil.offset(date, DateField.YEAR, offset);
                break;
            case MONTHLY:
                offsetDate = DateUtil.offset(date, DateField.MONTH, offset);
                break;
            case WEEK:
                offsetDate = DateUtil.offset(date, DateField.WEEK_OF_YEAR, offset);
                break;
            case DAILY:
                offsetDate = DateUtil.offset(date, DateField.DAY_OF_YEAR, offset);
                break;
            case HOURLY:
                offsetDate = DateUtil.offset(date, DateField.HOUR_OF_DAY, offset);
                break;
            default:
                throw new ServiceException("暂不支持的分析方式: " + analysisType);
        }
        // 按原 format 输出
        return DateUtil.formatDateTime(offsetDate);
    }
}
