package com.jianyou.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.ProjectDO;
import com.jianyou.common.core.domain.BaseOptionsVO;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.jianyou.biz.mapper.ProjectMapper;
import com.jianyou.biz.service.IProjectService;
import com.jianyou.biz.domain.vo.ProjectVO;
import com.jianyou.biz.domain.bo.ProjectBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RequiredArgsConstructor
@Service
public class ProjectServiceImpl implements IProjectService {

    private final ProjectMapper baseMapper;

    /**
     * 查询项目信息列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link ProjectVO }>
     */
    @Override
    public TableDataInfo<ProjectVO> queryPageList(ProjectBO bo, PageQuery pageQuery) {
        IPage<ProjectVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<ProjectDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getId()), ProjectDO::getId, bo.getId())
            .eq(StrUtil.isNotEmpty(bo.getId()), ProjectDO::getId, bo.getId())
        );
        return TableDataInfo.build(page);
    }

    /**
     * 查询项目信息列表All
     */
    @Override
    public List<ProjectVO> listAll(ProjectBO bo) {
        List<ProjectDO> list = baseMapper.selectList();
        return BeanUtil.copyToList(list, ProjectVO.class);
    }

    /**
     * 查询项目信息
     *
     * @param id id
     * @return {@link ProjectVO }
     */
    @Override
    public ProjectVO queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增项目信息
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean insertByBo(ProjectBO bo) {
        ProjectDO projectDO = new ProjectDO();
        BeanCopyUtils.copy(bo, projectDO);
        projectDO.create();
        projectDO.update();
        return baseMapper.insert(projectDO) > 0;
    }

    /**
     * 修改项目信息
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(ProjectBO bo) {
        ProjectDO projectDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(projectDO)) {
            return false;
        }
        BeanCopyUtils.copy(bo, projectDO);
        projectDO.update();
        return baseMapper.updateById(projectDO) > 0;
    }

    /**
     * 校验并批量删除项目信息信息
     *
     * @param ids     主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<BaseOptionsVO> option(ProjectBO bo) {
        List<ProjectDO> list = baseMapper.selectList(Wrappers.<ProjectDO>lambdaQuery()
            .eq(StrUtil.isNotEmpty(bo.getId()), ProjectDO::getId, bo.getId())
            .select(ProjectDO::getId, ProjectDO::getName)
        );
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(item -> new BaseOptionsVO(item.getId(), item.getName())).collect(Collectors.toList());
    }

}
