package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 计费配置对象 enms_billing_config
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("enms_billing_config")
public class BillingConfigDO {

    /**
     * ID
     */
    private String id;
    /**
     * 名称
     */
    private String name;
    /**
     * 能源类型
     */
    private String energyType;
    /**
     * 费率类型(标准计费;阶梯计费)
     */
    private String rateType;
    /**
     * 是否分段(1是0否)
     */
    private String peakValleyFlag;
    /**
     * 标准单价
     */
    private BigDecimal standardPrice;
    /**
     * 单位
     */
    private String unit;
    /**
     * 起始有效期
     */
    private Date startTime;
    /**
     * 截止有效期(为空永久有效)
     */
    private Date endTime;

    /** 配置版本 用来检测配置是否更改 */
    private String configVersions;

}
