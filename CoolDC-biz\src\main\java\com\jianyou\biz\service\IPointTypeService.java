package com.jianyou.biz.service;

import com.jianyou.biz.domain.vo.PointTypeVO;
import com.jianyou.biz.domain.bo.PointTypeBO;
import com.jianyou.common.core.domain.BaseOptionsVO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 点位类型Service接口
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
public interface IPointTypeService {


    /**
     * 查询点位类型列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link PointTypeVO }>
     */
    TableDataInfo<PointTypeVO> queryPageList(PointTypeBO bo, PageQuery pageQuery);

    /**
     * 查询点位类型
     *
     * @param id id
     * @return {@link PointTypeVO }
     */
     PointTypeVO queryById(String id);

    /**
     * 新增点位类型
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(PointTypeBO bo);

    /**
     * 修改点位类型
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(PointTypeBO bo);

    /**
     * 校验并批量删除点位类型信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 点位类型选项
     */
    List<BaseOptionsVO> option();
}
