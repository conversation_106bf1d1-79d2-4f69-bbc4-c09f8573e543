package com.jianyou.biz.service;

import com.jianyou.biz.domain.bo.BillingConfigBO;
import com.jianyou.biz.domain.vo.BillingConfigVO;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.Date;

/**
 * 计费配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IBillingConfigService {


    /**
     * 查询计费配置列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link BillingConfigVO }>
     */
    TableDataInfo<BillingConfigVO> queryPageList(BillingConfigBO bo, PageQuery pageQuery);

    /**
     * 查询计费配置
     *
     * @param id id
     * @return {@link BillingConfigVO }
     */
     BillingConfigVO queryById(String id);

    /**
     * 根据能源类型和日期查询计费配置
     *
     * @param energyType 能源类型
     * @param date       日期
     * @return {@link BillingConfigVO }
     */
    BillingConfigVO queryConfig(String energyType, Date date);

    /**
     * 新增计费配置
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(BillingConfigBO bo);

    /**
     * 修改计费配置
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(BillingConfigBO bo);

    /**
     * 校验并批量删除计费配置信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
