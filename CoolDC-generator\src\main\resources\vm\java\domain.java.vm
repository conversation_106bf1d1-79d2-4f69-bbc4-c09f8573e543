package ${packageName}.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.jianyou.common.core.domain.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

#foreach ($import in $importList)
import ${import};
#end
#if($table.crud || $table.sub)
#elseif($table.tree)
#end

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
    #set($Entity="BaseEntity")
#elseif($table.tree)
    #set($Entity="TreeEntity<${ClassName}>")
#end
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("${tableName}")
public class ${ClassName}DO extends BaseDO {

    private static final long serialVersionUID=1L;

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    /**
     * $column.columnComment
     */
#if($column.javaField=='delFlag')
    @TableLogic
#end
#if($column.javaField=='version')
    @Version
#end
#if($column.pk)
    @TableId(value = "$column.columnName")
#end
    private $column.javaType $column.javaField;
#end
#end

}
