package com.jianyou.web.controller.biz;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.jianyou.biz.domain.bo.ProjectBO;
import com.jianyou.biz.domain.vo.ProjectVO;
import com.jianyou.biz.service.IProjectService;
import com.jianyou.common.core.domain.BaseOptionsVO;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.utils.poi.ExcelUtil;
import com.jianyou.common.core.page.TableDataInfo;

/**
 * 项目信息
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/project")
public class ProjectController extends BaseController {

    private final IProjectService projectService;

    /**
     * 查询项目信息列表
     */
    @SaCheckPermission("biz:project:list")
    @GetMapping("/list")
    public TableDataInfo<ProjectVO> list(ProjectBO bo, PageQuery pageQuery) {
        return projectService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询项目信息列表All
     */
    @SaCheckPermission("biz:project:list")
    @GetMapping("/listAll")
    public R<List<ProjectVO>> listAll(ProjectBO bo) {
        return R.ok(projectService.listAll(bo));
    }

    /**
     * 导出项目信息列表
     */
    @SaCheckPermission("biz:project:export")
    @PostMapping("/export")
    public void export(ProjectBO bo, HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "项目信息", ProjectVO.class, response);
    }

    /**
     * 获取项目信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:project:query")
    @GetMapping("/{id}")
    public R<ProjectVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(projectService.queryById(id));
    }

    /**
     * 新增项目信息
     */
    @SaCheckPermission("biz:project:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProjectBO bo) {
        return toAjax(projectService.insertByBo(bo));
    }

    /**
     * 修改项目信息
     */
    @SaCheckPermission("biz:project:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProjectBO bo) {
        return toAjax(projectService.updateByBo(bo));
    }

    /**
     * 删除项目信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:project:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(projectService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 项目选项
     */
    @GetMapping("/option")
    public R<List<BaseOptionsVO>> option(ProjectBO bo) {
        return R.ok(projectService.option(bo));
    }
}
