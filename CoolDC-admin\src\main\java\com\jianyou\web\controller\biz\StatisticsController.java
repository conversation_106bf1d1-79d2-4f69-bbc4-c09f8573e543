package com.jianyou.web.controller.biz;

import com.alibaba.excel.EasyExcel;
import com.jianyou.biz.domain.bo.EnergyStatisticBO;
import com.jianyou.biz.domain.vo.EnergyStatisticVO;
import com.jianyou.biz.service.IStatisticsService;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.validate.QueryGroup;
import com.jianyou.common.exception.ServiceException;
import com.jianyou.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;


/**
 * 统计控制器
 *
 * <AUTHOR>
 * @date 2025/07/07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/statistics")
public class StatisticsController {

    private final IStatisticsService statisticsService;

    /**
     * 能耗统计
     */
    @GetMapping("/statisticsEnergy")
    public TableDataInfo<EnergyStatisticVO> statisticsEnergy(@Validated(QueryGroup.class) EnergyStatisticBO bo, PageQuery pageQuery) {
        return statisticsService.statisticsEnergy(bo, pageQuery, true);
    }

    /**
     * 导出统计列表
     */
    @PostMapping("/export")
    public void export(@Validated(QueryGroup.class) EnergyStatisticBO bo, HttpServletResponse response) {
        TableDataInfo<EnergyStatisticVO> tableDataInfo = statisticsService.statisticsEnergy(bo, null, false);
        List<EnergyStatisticVO> rows = tableDataInfo.getRows();
        if (rows == null || rows.isEmpty()) {
            ExcelUtil.exportExcel(new ArrayList<>(), "能耗统计", EnergyStatisticVO.class, response);
            return;
        }
        // 收集所有energyData的key作为表头
        Set<String> headKeySet = new LinkedHashSet<>();
        for (EnergyStatisticVO vo : rows) {
            if (vo.getEnergyData() != null) {
                headKeySet.addAll(vo.getEnergyData().keySet());
            }
        }
        // 构建动态表头
        List<List<String>> headList = new ArrayList<>();
        // 添加固定表头
        headList.add(Collections.singletonList("区域名称"));
        headList.add(Collections.singletonList("表具编号"));
        headList.add(Collections.singletonList("表具名称"));
        // 添加动态表头（energyData的key）
        for (String key : headKeySet) {
            headList.add(Collections.singletonList(key));
        }
        // 构建导出的数据
        List<List<Object>> dataList = new ArrayList<>();
        for (EnergyStatisticVO vo : rows) {
            List<Object> rowData = new ArrayList<>();
            // 添加固定列数据
            rowData.add(vo.getAreaName());
            rowData.add(vo.getMeterNumber());
            rowData.add(vo.getMeterName());
            // 添加动态列数据
            Map<String, Object> energyData = vo.getEnergyData();
            if (energyData != null) {
                for (String key : headKeySet) {
                    rowData.add(energyData.getOrDefault(key, ""));
                }
            } else {
                // 如果energyData为空，添加相应数量的空数据
                for (int i = 0; i < headKeySet.size(); i++) {
                    rowData.add("");
                }
            }
            dataList.add(rowData);
        }
        try {
            EasyExcel.write(response.getOutputStream())
                .head(headList)
                .sheet("能耗统计")
                .doWrite(dataList);
        } catch (IOException e) {
            throw new ServiceException("导出失败! 请联系管理员");
        }
    }


}
