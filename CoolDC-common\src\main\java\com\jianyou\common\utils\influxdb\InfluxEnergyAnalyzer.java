package com.jianyou.common.utils.influxdb;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jianyou.common.enums.AnalysisType;
import com.jianyou.common.exception.ServiceException;
import com.jianyou.common.utils.influxdb.domain.dto.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 时序库能耗分析工具
 *
 * <AUTHOR>
 * @date 2023/11/27
 */
public class InfluxEnergyAnalyzer {

    private InfluxEnergyAnalyzer() {
    }

    /**
     * 能耗分析
     *
     * @param influxResList influxdb数据 查询时需按时间倒序排序
     * @param analysisType  分析时间 只控制时间格式
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @param formatTime    是否按照分析方式格式化时间
     * @return {@link Map}<{@link String}, {@link Object}> k:时间 v:值
     */
    public static Map<String, Object> energyAnalyze(List<InfluxRes> influxResList, AnalysisType analysisType, Date startTime, Date endTime, boolean formatTime) {
        return analyse(influxResList, getTimeScope(analysisType, startTime, endTime), analysisType, formatTime);
    }

    /**
     * 分段分析 天为粒度
     *
     * @param influxResList influx数据 查询时需按时间倒序排序
     * @param subTime       分段配置
     * @param startTime     开始时间
     * @param endTime       结束时间
     * @return {@link Map}<{@link String}, {@link Object}> k: 时间 天为粒度 k: 值
     */
    public static PeakFlatLow subAnalyze(List<InfluxRes> influxResList, SubTime subTime, Date startTime, Date endTime) {
        // 分段统计 构造分段时间范围
        SubTimeScope subTimeScope = getSubTimeScope(subTime, startTime, endTime);

        // 峰
        List<TimeScope> peakScope = subTimeScope.getPeakScope();
        Map<String, Object> peakData = analyse(influxResList, peakScope, AnalysisType.DAILY, true);
        Map<String, Object> peakResult = mergeDailyData(peakData);

        // 平
        List<TimeScope> flatScope = subTimeScope.getFlatScope();
        Map<String, Object> flatData = analyse(influxResList, flatScope, AnalysisType.DAILY, false);
        Map<String, Object> flatResult = mergeDailyData(flatData);

        // 谷
        List<TimeScope> lowScope = subTimeScope.getLowScope();
        Map<String, Object> lowData = analyse(influxResList, lowScope, AnalysisType.DAILY, true);
        Map<String, Object> lowResult = mergeDailyData(lowData);

        return new PeakFlatLow(peakResult, flatResult, lowResult);
    }

    /**
     * 合并同一天的数据
     *
     * @param sourceData 源数据 key为时间字符串，value为值
     * @return 合并后的数据 key为日期字符串，value为该日期所有值的总和
     */
    private static Map<String, Object> mergeDailyData(Map<String, Object> sourceData) {
        Map<String, Object> result = new HashMap<>();
        sourceData.forEach((k, v) -> {
            String date = DateUtil.parseDate(k).toString();
            Object value = result.get(date);
            if (ObjectUtil.isEmpty(value)) {
                result.put(date, v);
            } else {
                result.put(date, Convert.toBigDecimal(v).add(Convert.toBigDecimal(value)));
            }
        });
        return result;
    }

    /**
     * 根据分析方式获取时间范围
     *
     * @param analysisType 分析模式
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return {@link List}<{@link TimeScope}>
     */
    private static List<TimeScope> getTimeScope(AnalysisType analysisType, Date startTime, Date endTime) {
        if (!ObjUtil.isAllNotEmpty(analysisType, startTime, endTime)) {
            return new ArrayList<>();
        }
        List<DateTime> dateTimes = DateUtil.rangeToList(startTime,
            DateUtil.offset(endTime, DateField.of(analysisType.getDateField()), 1),
            DateField.of(analysisType.getDateField()));
        return constructTimeScope(dateTimes);
    }

    /**
     * 获取分段时间范围
     *
     * @return {@link SubTimeScope}
     */
    private static SubTimeScope getSubTimeScope(SubTime subTime, Date startTime, Date endTime) {
        if (!ObjUtil.isAllNotEmpty(subTime, startTime, endTime)) {
            throw new ServiceException("参数缺失");
        }

        // 解析时间段
        List<FlatTime> peakTimeList = parseTimeSegments(subTime.getPeakTime());
        List<FlatTime> flatTimeList = parseTimeSegments(subTime.getFlatTime());
        List<FlatTime> lowTimeList = parseTimeSegments(subTime.getLowTime());

        // 生成每一天的时间范围
        List<TimeScope> peakScope = new ArrayList<>();
        List<TimeScope> flatScope = new ArrayList<>();
        List<TimeScope> lowScope = new ArrayList<>();
        List<DateTime> dateTimes = DateUtil.rangeToList(startTime, endTime, DateField.DAY_OF_MONTH);

        dateTimes.forEach(dateTime -> {
            String dateStr = dateTime.toString().split(" ")[0].trim();

            // 为每种类型的时间段生成TimeScope
            peakScope.addAll(generateTimeScopes(dateStr, peakTimeList));
            flatScope.addAll(generateTimeScopes(dateStr, flatTimeList));
            lowScope.addAll(generateTimeScopes(dateStr, lowTimeList));
        });

        return new SubTimeScope(peakScope, flatScope, lowScope);
    }

    /**
     * 解析时间段字符串为FlatTime列表
     *
     * @param timeSegments 时间段字符串，格式为"HH:mm:ss-HH:mm:ss,HH:mm:ss-HH:mm:ss"
     * @return 解析后的FlatTime列表
     */
    private static List<FlatTime> parseTimeSegments(String timeSegments) {
        List<FlatTime> timeList = new ArrayList<>();
        String[] segments = timeSegments.split(",");

        for (String segment : segments) {
            String[] timeArr = segment.split("-");
            String startTime = timeArr[0];
            String endTime = timeArr[1];
            timeList.add(new FlatTime(startTime, endTime));
        }

        return timeList;
    }

    /**
     * 根据日期和时间段列表生成TimeScope列表
     *
     * @param dateStr  日期字符串
     * @param timeList 时间段列表
     * @return 生成的TimeScope列表
     */
    private static List<TimeScope> generateTimeScopes(String dateStr, List<FlatTime> timeList) {
        List<TimeScope> timeScopes = new ArrayList<>();

        timeList.forEach(time -> {
            String startTimeStr = time.getStartTimeStr();
            String endTimeStr = time.getEndTimeStr();
            String fullStartTime = dateStr + " " + startTimeStr;
            String fullEndTime = dateStr + " " + endTimeStr;

            timeScopes.add(new TimeScope(
                DateUtil.parseDateTime(fullStartTime),
                DateUtil.parseDateTime(fullEndTime)
            ));
        });

        return timeScopes;
    }

    /**
     * 构建时间范围
     *
     * @param dateTimes 日期时间集合 为空则返回空的集合
     * @return {@link List}<{@link TimeScope}> 返回包含时间范围对象的集合 格式: 1-2 2-3 3-4
     */
    private static List<TimeScope> constructTimeScope(List<DateTime> dateTimes) {
        if (CollUtil.isEmpty(dateTimes)) {
            return new ArrayList<>();
        }
        List<TimeScope> timeScopes = new ArrayList<>();
        for (int i = 1; i < dateTimes.size(); i++) {
            TimeScope timeScope = new TimeScope();
            timeScope.setStartTime(dateTimes.get(i - 1));
            timeScope.setEndTime(dateTimes.get(i));
            timeScopes.add(timeScope);
        }
        return timeScopes;
    }

    /**
     * 分析
     *
     * @param influxResList influxdb查询数据
     * @param timeScopes    时间范围
     * @param analysisType  分析时间 只控制时间格式
     * @param formatTime    是否按照分析方式格式化时间
     * @return {@link Map}<{@link String}, {@link Object}> k:时间 v:值
     */
    private static Map<String, Object> analyse(List<InfluxRes> influxResList, List<TimeScope> timeScopes,
                                               AnalysisType analysisType, boolean formatTime) {
        if (!ObjUtil.isAllNotEmpty(analysisType, influxResList, timeScopes)) {
            Map<String, Object> map = new HashMap<>();
            timeScopes.forEach(timeScope -> {
                map.put(formatTime(analysisType, formatTime, timeScope.getStartTime()), "");
            });
            return map;
        }

        // 1. 预处理,转成时间值对象
        List<TimeValue> timeValues = influxResList.stream()
            .map(r -> new TimeValue(DateUtil.parseDateTime(r.getTime()), new BigDecimal(r.getValue())))
            .collect(Collectors.toList());

        TimeValueList timeValueList = new TimeValueList(timeValues);

        Map<String, Object> map = new LinkedHashMap<>();

        for (TimeScope timeScope : timeScopes) {
            Date startTime = timeScope.getStartTime();
            Date endTime = timeScope.getEndTime();

            TimeValue startTimeValue = timeValueList.findNearestValue(startTime);
            BigDecimal startValue = BigDecimal.ZERO;
            if (startTimeValue != null) {
                startValue = startTimeValue.getValue().setScale(2, RoundingMode.DOWN);
            }
            TimeValue endTimeValue = timeValueList.findNearestValue(endTime);
            BigDecimal endValue = BigDecimal.ZERO;
            if (endTimeValue != null) {
                endValue = endTimeValue.getValue().setScale(2, RoundingMode.DOWN);
            }

            BigDecimal resValue = endValue.subtract(startValue);
            // 格式时间
            String key = formatTime(analysisType, formatTime, startTime);

            map.put(key, resValue.toString());
        }

        return map;
    }

    /**
     * 格式时间
     *
     * @param analysisType 分析类型
     * @param formatTime   时间格式
     * @param time         时间
     * @return {@link String }
     */
    private static String formatTime(AnalysisType analysisType, boolean formatTime, Date time) {
        String key;
        if (formatTime) {
            if (AnalysisType.WEEK.getValue().equals(analysisType.getValue())) {
                String begTime = DateUtil.format(time, analysisType.getFormat());
                String stopTime = DateUtil.format(DateUtil.offset(time, DateField.of(analysisType.getDateField()), 1), analysisType.getFormat());
                key = begTime + "至" + stopTime;
            } else {
                key = DateUtil.format(time, analysisType.getFormat());
            }
        } else {
            key = DateUtil.formatDateTime(time);
        }
        return key;
    }
}
