package com.jianyou.biz.service;

import com.jianyou.biz.domain.bo.MeterReadingBO;
import com.jianyou.biz.domain.vo.MeterReadingVO;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 抄表记录Service接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface IMeterReadingService {


    /**
     * 查询抄表记录列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @param isPage    是否分页
     * @return {@link TableDataInfo }<{@link MeterReadingVO }>
     */
    TableDataInfo<MeterReadingVO> queryPageList(MeterReadingBO bo, PageQuery pageQuery, boolean isPage);

    /**
     * 校验并批量删除抄表记录信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 抄表 异步
     *
     * @param meterIds   仪表id
     * @param readingWay 抄表方式 1手动 0自动
     * @param isBilling  是否计费
     */
    void reading(List<String> meterIds, String readingWay, boolean isBilling);
}
