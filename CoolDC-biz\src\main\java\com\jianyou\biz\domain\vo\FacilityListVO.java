package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jianyou.common.annotation.ExcelDictFormat;
import com.jianyou.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备清单视图对象 biz_facility_list
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
public class FacilityListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 编号
     */
    @ExcelProperty(value = "编号")
    private String number;

    /**
     * 规格型号
     */
    @ExcelProperty(value = "规格型号")
    private String model;

    /**
     * 设备状态（使用中、备用、被更换)
     */
    @ExcelProperty(value = "设备状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "设备状态（使用中、备用、被更换)")
    private String facilityStatus;

    /**
     * 厂家名称
     */
    @ExcelProperty(value = "厂家名称")
    private String factoryName;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    private String linkman;

    /**
     * 联系人电话
     */
    @ExcelProperty(value = "联系人电话")
    private String linkmanNumber;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private String projectId;


}
