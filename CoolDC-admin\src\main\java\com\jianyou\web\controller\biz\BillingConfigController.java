package com.jianyou.web.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jianyou.biz.domain.bo.BillingConfigBO;
import com.jianyou.biz.domain.vo.BillingConfigVO;
import com.jianyou.biz.service.IBillingConfigService;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 计费配置
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/billingConfig")
public class BillingConfigController extends BaseController {

    private final IBillingConfigService billingConfigService;

    /**
     * 查询计费配置列表
     */
    @SaCheckPermission("biz:billingConfig:list")
    @GetMapping("/list")
    public TableDataInfo<BillingConfigVO> list(BillingConfigBO bo, PageQuery pageQuery) {
        return billingConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取计费配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:billingConfig:query")
    @GetMapping("/{id}")
    public R<BillingConfigVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(billingConfigService.queryById(id));
    }

    /**
     * 新增计费配置
     */
    @SaCheckPermission("biz:billingConfig:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BillingConfigBO bo) {
        return toAjax(billingConfigService.insertByBo(bo));
    }

    /**
     * 修改计费配置
     */
    @SaCheckPermission("biz:billingConfig:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BillingConfigBO bo) {
        return toAjax(billingConfigService.updateByBo(bo));
    }

    /**
     * 删除计费配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:billingConfig:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(billingConfigService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
