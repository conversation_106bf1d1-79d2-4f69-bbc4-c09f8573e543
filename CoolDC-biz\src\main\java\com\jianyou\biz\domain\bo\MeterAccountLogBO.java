package com.jianyou.biz.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 预存账户流水业务对象 enms_meter_account_log
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MeterAccountLogBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String id;

    /**
     * 表具id
     */
    @NotBlank(message = "表具id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String meterId;

    /**
     * 变动金额（正数=充值，负数=扣费）
     */
    @NotNull(message = "变动金额（正数=充值，负数=扣费）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal changeAmount;

    /**
     * 类型：1=充值，2=扣费，3=退费，4=手工调整
     */
    @NotBlank(message = "类型：1=充值，2=扣费，3=退费，4=手工调整不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 变动前余额
     */
    @NotNull(message = "变动前余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal beforeBalance;

    /**
     * 变动后余额
     */
    @NotNull(message = "变动后余额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal afterBalance;

    /**
     * 变动时间
     */
    @NotNull(message = "变动时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date createdTime;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startCreatedTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endCreatedTime;

}
