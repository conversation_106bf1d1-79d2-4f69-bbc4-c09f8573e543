package com.jianyou.web.controller.biz;

import java.util.ArrayList;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.utils.poi.ExcelUtil;
import com.jianyou.biz.domain.vo.BuildScheduleVO;
import com.jianyou.biz.domain.bo.BuildScheduleBO;
import com.jianyou.biz.service.IBuildScheduleService;
import com.jianyou.common.core.page.TableDataInfo;

/**
 * 施工进度
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/buildSchedule")
public class BuildScheduleController extends BaseController {

    private final IBuildScheduleService buildScheduleService;

    /**
     * 查询施工进度列表
     */
    @SaCheckPermission("biz:buildSchedule:list")
    @GetMapping("/list")
    public TableDataInfo<BuildScheduleVO> list(BuildScheduleBO bo, PageQuery pageQuery) {
        return buildScheduleService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出施工进度列表
     */
    @SaCheckPermission("biz:buildSchedule:export")
    @PostMapping("/export")
    public void export(BuildScheduleBO bo, HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "施工进度", BuildScheduleVO.class, response);
    }

    /**
     * 获取施工进度详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:buildSchedule:query")
    @GetMapping("/{id}")
    public R<BuildScheduleVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(buildScheduleService.queryById(id));
    }

    /**
     * 新增施工进度
     */
    @SaCheckPermission("biz:buildSchedule:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BuildScheduleBO bo) {
        return toAjax(buildScheduleService.insertByBo(bo));
    }

    /**
     * 修改施工进度
     */
    @SaCheckPermission("biz:buildSchedule:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BuildScheduleBO bo) {
        return toAjax(buildScheduleService.updateByBo(bo));
    }

    /**
     * 删除施工进度
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:buildSchedule:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(buildScheduleService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
