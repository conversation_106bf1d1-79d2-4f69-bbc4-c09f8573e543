package com.jianyou.biz.service;

import com.jianyou.biz.domain.bo.TieredConfigBO;
import com.jianyou.biz.domain.vo.TieredConfigVO;

import java.util.List;

/**
 * 阶梯配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface ITieredConfigService {

    /**
     * 根据计费配置ID查询阶梯配置列表
     *
     * @param billingConfigId 计费配置ID
     * @return 阶梯配置列表
     */
    List<TieredConfigVO> queryByBillingConfigId(String billingConfigId);

    /**
     * 批量新增阶梯配置
     *
     * @param tieredConfigs 阶梯配置列表
     * @return 结果
     */
    boolean insertBatch(List<TieredConfigBO> tieredConfigs);

    /**
     * 根据计费配置ID删除阶梯配置
     *
     * @param billingConfigId 计费配置ID
     * @return 结果
     */
    boolean deleteByBillingConfigId(String billingConfigId);
}
