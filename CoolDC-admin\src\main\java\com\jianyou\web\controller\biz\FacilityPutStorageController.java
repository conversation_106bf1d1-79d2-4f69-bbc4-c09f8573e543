package com.jianyou.web.controller.biz;

import java.util.ArrayList;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.utils.poi.ExcelUtil;
import com.jianyou.biz.domain.vo.FacilityPutStorageVO;
import com.jianyou.biz.domain.bo.FacilityPutStorageBO;
import com.jianyou.biz.service.IFacilityPutStorageService;
import com.jianyou.common.core.page.TableDataInfo;

/**
 * 设备入库记录
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/facilityPutStorage")
public class FacilityPutStorageController extends BaseController {

    private final IFacilityPutStorageService facilityPutStorageService;

    /**
     * 查询设备入库记录列表
     */
    @SaCheckPermission("biz:facilityPutStorage:list")
    @GetMapping("/list")
    public TableDataInfo<FacilityPutStorageVO> list(FacilityPutStorageBO bo, PageQuery pageQuery) {
        return facilityPutStorageService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出设备入库记录列表
     */
    @SaCheckPermission("biz:facilityPutStorage:export")
    @PostMapping("/export")
    public void export(FacilityPutStorageBO bo, HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "设备入库记录", FacilityPutStorageVO.class, response);
    }

    /**
     * 获取设备入库记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:facilityPutStorage:query")
    @GetMapping("/{id}")
    public R<FacilityPutStorageVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(facilityPutStorageService.queryById(id));
    }

    /**
     * 新增设备入库记录
     */
    @SaCheckPermission("biz:facilityPutStorage:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FacilityPutStorageBO bo) {
        return toAjax(facilityPutStorageService.insertByBo(bo));
    }

    /**
     * 修改设备入库记录
     */
    @SaCheckPermission("biz:facilityPutStorage:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FacilityPutStorageBO bo) {
        return toAjax(facilityPutStorageService.updateByBo(bo));
    }

    /**
     * 删除设备入库记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:facilityPutStorage:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(facilityPutStorageService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
