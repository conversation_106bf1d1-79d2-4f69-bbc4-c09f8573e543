package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.BuildPlanDO;
import com.jianyou.biz.domain.BuildScheduleDO;
import com.jianyou.biz.mapper.BuildPlanMapper;
import com.jianyou.common.core.service.OssService;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.jianyou.biz.mapper.BuildScheduleMapper;
import com.jianyou.biz.service.IBuildScheduleService;
import com.jianyou.biz.domain.vo.BuildScheduleVO;
import com.jianyou.biz.domain.bo.BuildScheduleBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 施工进度Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RequiredArgsConstructor
@Service
public class BuildScheduleServiceImpl implements IBuildScheduleService {

    private final BuildScheduleMapper baseMapper;
    private final BuildPlanMapper buildPlanMapper;
    private final OssService ossService;

    /**
     * 查询施工进度列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link BuildScheduleVO }>
     */
    @Override
    public TableDataInfo<BuildScheduleVO> queryPageList(BuildScheduleBO bo, PageQuery pageQuery){
        IPage<BuildScheduleVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<BuildScheduleDO>lambdaQuery()
            .eq(StrUtil.isNotEmpty(bo.getBuildPlanId()), BuildScheduleDO::getBuildPlanId, bo.getBuildPlanId())
            .eq(StrUtil.isNotEmpty(bo.getProjectId()), BuildScheduleDO::getProjectId, bo.getProjectId())
        );
        List<BuildScheduleVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new TableDataInfo<>();
        }
        Map<String, BuildPlanDO> map = buildPlanMapper.nameDict(records.stream().map(BuildScheduleVO::getBuildPlanId).collect(Collectors.toList()));
        records.forEach(record -> {
            BuildPlanDO buildPlanDO = map.get(record.getBuildPlanId());
            if (buildPlanDO != null){
                record.setBuildPlanName(buildPlanDO.getName());
            }
            // 照片
            record.setOssIds(ossService.selectUrlByIds(record.getOssIds()));
        });
        return TableDataInfo.build(page);
    }

    /**
     * 查询施工进度
     *
     * @param id id
     * @return {@link BuildScheduleVO }
     */
    @Override
    public BuildScheduleVO queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增施工进度
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean insertByBo(BuildScheduleBO bo){
        BuildScheduleDO buildScheduleDO = new BuildScheduleDO();
        BeanCopyUtils.copy(bo,buildScheduleDO);
        buildScheduleDO.create();
        buildScheduleDO.update();
        return baseMapper.insert(buildScheduleDO) > 0;
    }

    /**
     * 修改施工进度
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(BuildScheduleBO bo){
        BuildScheduleDO buildScheduleDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(buildScheduleDO)) {
            return false;
        }
        BeanCopyUtils.copy(bo,buildScheduleDO);
        buildScheduleDO.update();
        return baseMapper.updateById(buildScheduleDO) > 0;
    }

    /**
     * 校验并批量删除施工进度信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid){
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
