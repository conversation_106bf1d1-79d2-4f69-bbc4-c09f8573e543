package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.jianyou.common.core.domain.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 设备清单对象 biz_facility_list
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_facility_list")
public class FacilityListDO extends BaseDO {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private String id;
    /**
     * 名称
     */
    private String name;
    /**
     * 编号
     */
    private String number;
    /**
     * 规格型号
     */
    private String model;
    /**
     * 设备状态（使用中、备用、被更换)
     */
    private String facilityStatus;
    /**
     * 厂家名称
     */
    private String factoryName;
    /**
     * 联系人
     */
    private String linkman;
    /**
     * 联系人电话
     */
    private String linkmanNumber;
    /**
     * 项目id
     */
    private String projectId;

}
