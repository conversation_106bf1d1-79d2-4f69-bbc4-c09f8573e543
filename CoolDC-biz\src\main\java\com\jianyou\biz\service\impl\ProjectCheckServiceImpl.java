package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.ProjectCheckDO;
import com.jianyou.biz.domain.ProjectDO;
import com.jianyou.biz.mapper.ProjectMapper;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.jianyou.biz.mapper.ProjectCheckMapper;
import com.jianyou.biz.service.IProjectCheckService;
import com.jianyou.biz.domain.vo.ProjectCheckVO;
import com.jianyou.biz.domain.bo.ProjectCheckBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目验收Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RequiredArgsConstructor
@Service
public class ProjectCheckServiceImpl implements IProjectCheckService {

    private final ProjectCheckMapper baseMapper;
    private final ProjectMapper projectMapper;

    /**
     * 查询项目验收列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link ProjectCheckVO }>
     */
    @Override
    public TableDataInfo<ProjectCheckVO> queryPageList(ProjectCheckBO bo, PageQuery pageQuery) {
        IPage<ProjectCheckVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<ProjectCheckDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getAcceptor()), ProjectCheckDO::getAcceptor, bo.getAcceptor())
            .eq(StrUtil.isNotEmpty(bo.getProjectId()), ProjectCheckDO::getProjectId, bo.getProjectId())
        );
        List<ProjectCheckVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new TableDataInfo<>();
        }
        Map<String, ProjectDO> map = projectMapper.nameDict(records.stream().map(ProjectCheckVO::getProjectId).collect(Collectors.toList()));
        records.forEach(record -> {
            ProjectDO projectDO = map.get(record.getProjectId());
            if (projectDO != null) {
                record.setProjectName(projectDO.getName());
            }
        });
        return TableDataInfo.build(page);
    }

    /**
     * 查询项目验收
     *
     * @param id id
     * @return {@link ProjectCheckVO }
     */
    @Override
    public ProjectCheckVO queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增项目验收
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean insertByBo(ProjectCheckBO bo) {
        ProjectCheckDO projectCheckDO = new ProjectCheckDO();
        BeanCopyUtils.copy(bo, projectCheckDO);
        projectCheckDO.create();
        projectCheckDO.update();
        return baseMapper.insert(projectCheckDO) > 0;
    }

    /**
     * 修改项目验收
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(ProjectCheckBO bo) {
        ProjectCheckDO projectCheckDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(projectCheckDO)) {
            return false;
        }
        BeanCopyUtils.copy(bo, projectCheckDO);
        projectCheckDO.update();
        return baseMapper.updateById(projectCheckDO) > 0;
    }

    /**
     * 校验并批量删除项目验收信息
     *
     * @param ids     主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
