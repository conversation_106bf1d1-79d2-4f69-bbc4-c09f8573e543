package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.AreaDO;
import com.jianyou.biz.domain.bo.AreaBO;
import com.jianyou.biz.domain.vo.AreaVO;
import com.jianyou.biz.mapper.AreaMapper;
import com.jianyou.biz.service.IAreaService;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.exception.ServiceException;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 区域Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RequiredArgsConstructor
@Service
public class AreaServiceImpl implements IAreaService {

    private final AreaMapper baseMapper;

    /**
     * 查询区域列表
     *
     * @param bo        bo
     * @return {@link TableDataInfo }<{@link AreaVO }>
     */
    @Override
    public List<Tree<String>> queryPageList(AreaBO bo){
        List<AreaVO> areas = baseMapper.selectVoList(Wrappers.<AreaDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getName()), AreaDO::getName, bo.getName())
        );
        if (CollUtil.isEmpty(areas)) {
            return new ArrayList<>();
        }
        return buildTree(areas);
    }

    /**
     * 构建树
     *
     * @param areas 区域列表
     * @return {@link List}<{@link Tree}<{@link String}>>
     */
    private List<Tree<String>> buildTree(List<AreaVO> areas) {
        // 构建树结构
        List<TreeNode<String>> nodes = areas.stream().map(area -> {
            // 拓展属性
            Map<String, Object> extraMap = new HashMap<>();
            extraMap.put("name", area.getName());
            return new TreeNode<String>()
                .setId(area.getId())
                .setParentId(area.getParentId())
                .setWeight(area.getWeight())
                .setExtra(extraMap);
        }).collect(Collectors.toList());
        return TreeUtil.build(nodes, "0");
    }

    /**
     * 查询区域
     *
     * @param id id
     * @return {@link AreaVO }
     */
    @Override
    public AreaVO queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增区域
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean insertByBo(AreaBO bo){
        AreaDO areaDO = new AreaDO();
        BeanCopyUtils.copy(bo,areaDO);
        return baseMapper.insert(areaDO) > 0;
    }

    /**
     * 修改区域
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(AreaBO bo){
        AreaDO areaDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(areaDO)) {
            return false;
        }
        BeanCopyUtils.copy(bo,areaDO);
        return baseMapper.updateById(areaDO) > 0;
    }

    /**
     * 校验并批量删除区域信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid){
        if (isValid) {
            // 校验是否存在子区域
            for (String id : ids) {
                // 查询是否有子区域
                Long count = baseMapper.selectCount(Wrappers.<AreaDO>lambdaQuery()
                    .eq(AreaDO::getParentId, id));
                if (count > 0) {
                    // 存在子区域，不能删除
                    String areaName = baseMapper.selectById(id).getName();
                    throw new ServiceException("区域[" + areaName + "]存在子区域，不能删除");
                }
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
