# InfluxDB 时间开窗聚合测试指南

## 🎯 测试目标

基于您提供的Flux查询语句，测试新实现的时间开窗聚合功能：

```flux
import "timezone"
import "math"

option location = timezone.location(name: "Asia/Shanghai")

from(bucket: "test")
  |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
  |> filter(fn: (r) => r["_measurement"] == "meterVolume")
  |> filter(fn: (r) => r["_field"] == "volume")
  |> aggregateWindow(every: 1d, fn: sum, createEmpty: false)
  |> yield(name: "daily_sum")
```

## 🚀 快速开始

### 方式1：通过REST API测试

启动应用后，可以通过以下API端点进行测试：

#### 1. 健康检查
```bash
GET /api/influx/test/health
```
检查InfluxDB连接是否正常。

#### 2. 执行所有测试
```bash
GET /api/influx/test/run-all
```
执行所有预定义的测试用例。

#### 3. 测试按天聚合（对应您的查询）
```bash
GET /api/influx/test/daily-volume
```
执行与您的Flux查询等效的测试。

#### 4. 自定义参数测试
```bash
POST /api/influx/test/custom-aggregation
Content-Type: application/x-www-form-urlencoded

measurement=METER_VOLUME&field=VOLUME&daysBack=7&windowDuration=1d
```

参数说明：
- `measurement`: 测量名称 (METER_VOLUME 对应 meterVolume)
- `field`: 字段名称 (VOLUME 对应 volume)
- `daysBack`: 查询多少天前的数据 (默认7天)
- `windowDuration`: 时间窗口 (1d=1天, 1h=1小时, 15m=15分钟)
- `tags`: 可选，指定设备标签列表

#### 5. 查看生成的Flux查询语句
```bash
POST /api/influx/test/get-flux-query
Content-Type: application/x-www-form-urlencoded

measurement=METER_VOLUME&field=VOLUME&daysBack=7&windowDuration=1d
```

### 方式2：直接调用Java方法

```java
@Autowired
private InfluxUtils influxUtils;

@Autowired
private WindowAggregationTest windowAggregationTest;

// 执行所有测试
windowAggregationTest.runAllTests();

// 或者直接调用聚合方法
List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
    InfluxdbMeasurement.METER_VOLUME,  // meterVolume
    null,                              // 所有设备
    InfluxdbField.VOLUME,              // volume字段
    DateUtil.offsetDay(new Date(), -7), // 7天前
    new Date(),                        // 现在
    "1d",                             // 1天窗口
    null                              // 无额外分组
);
```

## 📊 测试用例说明

### 测试1：按天聚合体积数据
- **对应查询**: 您提供的Flux查询
- **功能**: 查询所有设备的volume字段，按天进行sum聚合
- **时区**: 上海时区（UTC+8）
- **方法**: `testDailyVolumeAggregation()`

### 测试2：指定设备的按天聚合
- **功能**: 只查询指定设备的数据
- **设备**: device001, device002, meter001
- **方法**: `testSpecificDeviceDailyAggregation()`

### 测试3：按小时聚合
- **功能**: 更细粒度的时间窗口测试
- **窗口**: 1小时
- **时间范围**: 最近24小时
- **方法**: `testHourlyVolumeAggregation()`

### 测试4：多维度分组聚合
- **功能**: 测试按多个标签维度分组
- **分组维度**: region, building, deviceType
- **方法**: `testMultiDimensionAggregation()`

## 🔧 配置要求

确保您的应用配置中包含：

```yaml
# application.yml
influxdb:
  bucket: test  # 对应您查询中的bucket
  # 其他InfluxDB配置...
```

## 📝 生成的Flux查询示例

基于您的查询，系统会生成类似这样的Flux语句：

```flux
import "timezone"
import "math"

option location = timezone.location(name: "Asia/Shanghai")

from(bucket: "test")
  |> range(start: 2025-01-01T00:00:00Z, stop: 2025-01-08T00:00:01Z)
  |> filter(fn: (r) => r["_measurement"] == "meterVolume")
  |> filter(fn: (r) => r["_field"] == "volume")
  |> aggregateWindow(every: 1d, fn: sum, createEmpty: false)
  |> sort(columns: ["_time"])
  |> yield(name: "aggregated_result")
```

## 🐛 故障排除

### 1. 无数据返回
- 检查bucket配置是否正确
- 确认时间范围内是否有meterVolume测量的volume字段数据
- 验证InfluxDB连接配置

### 2. 连接失败
- 检查InfluxDB服务是否运行
- 验证URL、token、org等配置
- 查看网络连接

### 3. 查询错误
- 检查measurement和field枚举是否正确
- 验证时间格式和时区设置
- 查看应用日志获取详细错误信息

## 📋 测试检查清单

- [ ] InfluxDB连接正常 (`/api/influx/test/health`)
- [ ] 基础按天聚合测试通过 (`/api/influx/test/daily-volume`)
- [ ] 自定义参数测试正常 (`/api/influx/test/custom-aggregation`)
- [ ] 生成的Flux查询语句正确 (`/api/influx/test/get-flux-query`)
- [ ] 日志输出包含预期的聚合结果
- [ ] 时区处理正确（上海时区，从0点开始）

## 📞 支持

如果测试过程中遇到问题，请：

1. 查看应用日志获取详细错误信息
2. 使用 `/api/influx/test/get-flux-query` 检查生成的查询语句
3. 验证InfluxDB中是否有对应的测试数据
4. 检查配置文件中的InfluxDB连接参数
