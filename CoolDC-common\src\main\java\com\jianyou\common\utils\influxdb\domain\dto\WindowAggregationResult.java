package com.jianyou.common.utils.influxdb.domain.dto;

import lombok.Data;

import java.util.Map;

/**
 * 时间窗口聚合结果
 *
 * <AUTHOR> Assistant
 * @date 2025/01/02
 */
@Data
public class WindowAggregationResult {

    /** 标签 */
    private String tag;
    
    /** 字段名 */
    private String field;
    
    /** 聚合值 */
    private Double aggregatedValue;
    
    /** 窗口开始时间 */
    private String windowStart;
    
    /** 窗口结束时间 */
    private String windowStop;
    
    /** 其他维度标签 */
    private Map<String, String> dimensions;

}
