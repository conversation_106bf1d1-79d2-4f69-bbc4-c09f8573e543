package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.PointTypeDO;
import com.jianyou.common.core.domain.BaseOptionsVO;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.jianyou.biz.mapper.PointTypeMapper;
import com.jianyou.biz.service.IPointTypeService;
import com.jianyou.biz.domain.vo.PointTypeVO;
import com.jianyou.biz.domain.bo.PointTypeBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 点位类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@RequiredArgsConstructor
@Service
public class PointTypeServiceImpl implements IPointTypeService {

    private final PointTypeMapper baseMapper;

    /**
     * 查询点位类型列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link PointTypeVO }>
     */
    @Override
    public TableDataInfo<PointTypeVO> queryPageList(PointTypeBO bo, PageQuery pageQuery){
        IPage<PointTypeVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<PointTypeDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getId()), PointTypeDO::getId, bo.getId())
            .eq(StrUtil.isNotEmpty(bo.getId()), PointTypeDO::getId, bo.getId())
        );
        List<PointTypeVO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new TableDataInfo<>();
        }
        records.forEach(record -> {
        });
        return TableDataInfo.build(page);
    }

    /**
     * 查询点位类型
     *
     * @param id id
     * @return {@link PointTypeVO }
     */
    @Override
    public PointTypeVO queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增点位类型
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean insertByBo(PointTypeBO bo){
        PointTypeDO pointTypeDO = new PointTypeDO();
        BeanCopyUtils.copy(bo,pointTypeDO);
        return baseMapper.insert(pointTypeDO) > 0;
    }

    /**
     * 修改点位类型
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(PointTypeBO bo){
        PointTypeDO pointTypeDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(pointTypeDO)) {
            return false;
        }
        BeanCopyUtils.copy(bo,pointTypeDO);
        return baseMapper.updateById(pointTypeDO) > 0;
    }

    /**
     * 校验并批量删除点位类型信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid){
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 点位类型选项
     */
    @Override
    public List<BaseOptionsVO> option() {
        List<PointTypeDO> list = baseMapper.selectList(Wrappers.<PointTypeDO>lambdaQuery()
            .select(PointTypeDO::getId, PointTypeDO::getName)
        );
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(item -> new BaseOptionsVO(item.getId(), item.getName())).collect(Collectors.toList());
    }

}
