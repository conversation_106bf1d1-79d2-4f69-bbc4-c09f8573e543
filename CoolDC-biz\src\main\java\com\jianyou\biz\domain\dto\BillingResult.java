package com.jianyou.biz.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 计费结果
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BillingResult {
    /** 计费明细 */
    private List<ChargeDetail> details;
    /** 总费用 */
    private BigDecimal totalFee;
    /** 最终累计量（用于阶梯累计） */
    private BigDecimal finalCumulativeVolume;
    /** 总能耗量（所有读数段的用量之和） */
    private BigDecimal totalVolume;
}
