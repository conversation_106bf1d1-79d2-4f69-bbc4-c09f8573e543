package com.jianyou.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.MeterDO;
import com.jianyou.biz.domain.bo.HomeBO;
import com.jianyou.biz.domain.vo.HomeMeterDataVO;
import com.jianyou.biz.domain.vo.HomeMeterListVO;
import com.jianyou.biz.domain.vo.HomeMeterOnLineStatusVO;
import com.jianyou.biz.mapper.AreaMapper;
import com.jianyou.biz.mapper.MeterMapper;
import com.jianyou.biz.service.IHomeService;
import com.jianyou.common.enums.InfluxdbField;
import com.jianyou.common.enums.InfluxdbMeasurement;
import com.jianyou.common.utils.influxdb.InfluxUtils;
import com.jianyou.common.utils.influxdb.domain.dto.InfluxRes;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 首页服务
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@Service
@RequiredArgsConstructor
public class HomeServiceImpl implements IHomeService {

    private final MeterMapper meterMapper;
    private final AreaMapper areaMapper;
    private final InfluxUtils influxUtils;

    /**
     * 表具列表
     */
    @Override
    public HomeMeterDataVO subList(HomeBO bo) {
        List<MeterDO> meters = meterMapper.selectList(Wrappers.<MeterDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getMeterName()), MeterDO::getName, bo.getMeterName())
            .eq(StrUtil.isNotEmpty(bo.getEnergyType()), MeterDO::getEnergyType, bo.getEnergyType())
            .in(CollUtil.isNotEmpty(bo.getAreaIds()), MeterDO::getAreaId, bo.getAreaIds())
        );
        if (CollUtil.isEmpty(meters)) {
            return new HomeMeterDataVO();
        }
        Date now = new Date();
        DateTime startTime = DateUtil.beginOfDay(now);
        List<String> numbers = meters.stream().map(MeterDO::getNumber).collect(Collectors.toList());
        // 表具能耗
        Map<String, List<InfluxRes>> energyMap = influxUtils.selectBatchLatest(InfluxdbMeasurement.METER,
            numbers,
            Collections.singletonList(InfluxdbField.READING),
            startTime, now);
        // 获取上一个单位的时间
        Map<String, List<InfluxRes>> lastMap = influxUtils.selectBatchLatest(InfluxdbMeasurement.METER,
            numbers,
            Collections.singletonList(InfluxdbField.READING),
            null, startTime);

        AtomicReference<BigDecimal> sumEnergy = new AtomicReference<>(BigDecimal.ZERO);
        List<HomeMeterListVO> meterSubList = meters.stream().map(meter -> {
            String number = meter.getNumber();
            HomeMeterListVO homeMeterListVO = BeanUtil.copyProperties(meter, HomeMeterListVO.class);
            homeMeterListVO.setMeterId(meter.getId());
            homeMeterListVO.setMeterNumber(meter.getNumber());
            homeMeterListVO.setMeterName(meter.getName());
            homeMeterListVO.setTime(meter.getMeterTime());
            homeMeterListVO.setAreaName(areaMapper.getAreaFullPath(meter.getAreaId()));
            List<InfluxRes> energyList = energyMap.get(number);
            if (energyList == null) {
                return homeMeterListVO;
            }
            BigDecimal endEnergy = new BigDecimal(energyList.get(0).getValue()).setScale(2, RoundingMode.DOWN);
            BigDecimal startEnergy;
            List<InfluxRes> lastList = lastMap.get(number);
            if (lastList != null) {
                startEnergy = new BigDecimal(lastList.get(0).getValue()).setScale(2, RoundingMode.DOWN);
            } else {
                startEnergy = new BigDecimal(energyList.get(0).getValue()).setScale(2, RoundingMode.DOWN);
            }
            homeMeterListVO.setMeterRecord(endEnergy);
            homeMeterListVO.setTime(energyList.get(0).getTime());
            BigDecimal toDayEnergy = endEnergy.subtract(startEnergy);
            homeMeterListVO.setToDayEnergy(toDayEnergy);
            sumEnergy.set(sumEnergy.get().add(toDayEnergy));
            return homeMeterListVO;
        }).collect(Collectors.toList());

        // 切割为3个子集合
        List<List<HomeMeterListVO>> result = new ArrayList<>();

        int total = meterSubList.size();
        // 每份的大小，最后一组可能少
        int partSize = (int) Math.ceil((double) total / 3);
        for (int i = 0; i < 3; i++) {
            int fromIndex = i * partSize;
            int toIndex = Math.min(fromIndex + partSize, total);

            if (fromIndex < total) {
                result.add(meterSubList.subList(fromIndex, toIndex));
            } else {
                // 补空集合
                result.add(Collections.emptyList());
            }
        }
        return new HomeMeterDataVO(sumEnergy.get(), result);
    }

    /**
     * 表具在线情况
     */
    @Override
    public HomeMeterOnLineStatusVO meterOnlineStatus(HomeBO bo) {
        List<MeterDO> meters = meterMapper.selectList(Wrappers.<MeterDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getMeterName()), MeterDO::getName, bo.getMeterName())
            .eq(StrUtil.isNotEmpty(bo.getEnergyType()), MeterDO::getEnergyType, bo.getEnergyType())
            .in(CollUtil.isNotEmpty(bo.getAreaIds()), MeterDO::getAreaId, bo.getAreaIds()));
        // 总数
        long meterSum = meters.size();
        // 在线数量
        long online = meters.stream().filter(meter -> "1".equals(meter.getOnlineStatus())).count();
        // 离线数量
        long offline = meterSum - online;
        return new HomeMeterOnLineStatusVO(meterSum, online, offline);
    }
}
