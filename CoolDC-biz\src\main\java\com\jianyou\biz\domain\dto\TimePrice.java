package com.jianyou.biz.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalTime;

/**
 * 时间段价格
 *
 * <AUTHOR>
 * @date 2025/07/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TimePrice {
    private LocalTime start;
    private LocalTime end;
    // 峰平谷加价
    private BigDecimal price;
    // 峰/平/谷
    private String name;
}
