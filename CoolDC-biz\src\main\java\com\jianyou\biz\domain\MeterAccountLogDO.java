package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 预存账户流水对象 enms_meter_account_log
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@TableName("enms_meter_account_log")
public class MeterAccountLogDO {

    /**
     * ID
     */
    private String id;
    /**
     * 表具id
     */
    private String meterId;
    /**
     * 变动金额（正数=充值，负数=扣费）
     */
    private BigDecimal changeAmount;
    /**
     * 类型：1=充值，2=扣费，3=退费，4=手工调整
     */
    private String type;
    /**
     * 变动前余额
     */
    private BigDecimal beforeBalance;
    /**
     * 变动后余额
     */
    private BigDecimal afterBalance;
    /**
     * 变动时间
     */
    private Date createdTime;
    /**
     * 备注
     */
    private String remark;

}
