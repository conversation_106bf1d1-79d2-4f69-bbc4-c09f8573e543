package com.jianyou.biz.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.ProjectDO;
import com.jianyou.biz.domain.vo.ProjectVO;
import com.jianyou.common.core.mapper.BaseMapperPlus;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface ProjectMapper extends BaseMapperPlus<ProjectMapper, ProjectDO, ProjectVO> {

    default Map<String, ProjectDO> nameDict(List<String> ids) {
        List<ProjectDO> list = selectList(Wrappers.<ProjectDO>lambdaQuery()
            .in(CollUtil.isNotEmpty(ids), ProjectDO::getId, ids)
        );
        return list.stream().collect(Collectors.toMap(ProjectDO::getId, projectDO -> projectDO));
    }
}
