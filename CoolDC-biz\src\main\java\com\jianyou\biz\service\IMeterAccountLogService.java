package com.jianyou.biz.service;


import com.jianyou.biz.domain.bo.MeterAccountLogBO;
import com.jianyou.biz.domain.vo.MeterAccountLogVO;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;

import java.math.BigDecimal;

/**
 * 预存账户流水Service接口
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface IMeterAccountLogService {

    /**
     * 查询预存账户流水列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link MeterAccountLogVO }>
     */
    TableDataInfo<MeterAccountLogVO> queryPageList(MeterAccountLogBO bo, PageQuery pageQuery);

    /**
     * 记录表具账户变动流水
     *
     * @param meterId       仪表id
     * @param beforeBalance 原本余额
     * @param changeAmount  变动金额
     * @param type          类型：1=充值，2=扣费，3=退费，4=手工调整
     * @param remark        备注
     * @return 结果
     */
    Boolean recordAccountChange(String meterId, BigDecimal beforeBalance, BigDecimal changeAmount, String type, String remark);
}
