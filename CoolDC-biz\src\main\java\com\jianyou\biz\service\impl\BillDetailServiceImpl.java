package com.jianyou.biz.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.BillDetailDO;
import com.jianyou.biz.domain.bo.BillDetailBO;
import com.jianyou.biz.domain.vo.BillDetailVO;
import com.jianyou.biz.mapper.BillDetailMapper;
import com.jianyou.biz.service.IBillDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 账单详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@RequiredArgsConstructor
@Service
public class BillDetailServiceImpl implements IBillDetailService {

    private final BillDetailMapper billDetailMapper;

    /**
     * 查询账单详情列表
     */
    @Override
    public List<BillDetailVO> queryList(BillDetailBO bo) {
        List<BillDetailDO> billDetails = billDetailMapper.selectList(Wrappers.<BillDetailDO>lambdaQuery()
            .eq(BillDetailDO::getBillId, bo.getBillId())
        );
        return BeanUtil.copyToList(billDetails, BillDetailVO.class);
    }
}
