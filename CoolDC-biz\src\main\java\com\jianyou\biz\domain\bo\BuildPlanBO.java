package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 施工计划业务对象 biz_build_plan
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuildPlanBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 项目ID
     */
    @NotBlank(message = "项目不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectId;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 描述
     */
    @NotBlank(message = "描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String details;

    /**
     * 分项负责人
     */
    @NotBlank(message = "分项负责人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String principal;

    /**
     * 分项负责人电话
     */
    @NotBlank(message = "分项负责人电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String principalNumber;

    /**
     * 施工开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "施工开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 施工截止时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "施工截止时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endTime;

    /**
     * 变更状态（未确认、确认、否认、废除）
     */
    private String changeStatus;


}
