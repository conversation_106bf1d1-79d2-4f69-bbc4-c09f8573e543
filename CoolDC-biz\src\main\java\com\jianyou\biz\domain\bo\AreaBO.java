package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 区域业务对象 enms_area
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AreaBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 父级区域ID
     */
    @NotBlank(message = "父级区域ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentId;

    /**
     * 同级区域排序
     */
    @NotNull(message = "同级区域排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long weight;


}
