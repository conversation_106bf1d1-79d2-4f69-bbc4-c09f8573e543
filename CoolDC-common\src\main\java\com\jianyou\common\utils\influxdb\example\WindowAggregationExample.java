package com.jianyou.common.utils.influxdb.example;

import com.jianyou.common.enums.InfluxdbField;
import com.jianyou.common.enums.InfluxdbMeasurement;
import com.jianyou.common.utils.influxdb.InfluxUtils;
import com.jianyou.common.utils.influxdb.domain.dto.WindowAggregationResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 时间开窗聚合使用示例
 *
 * <AUTHOR> Assistant
 * @date 2025/01/02
 */
@Component
@RequiredArgsConstructor
public class WindowAggregationExample {

    private final InfluxUtils influxUtils;

    /**
     * 示例1：按小时聚合单个设备的读数
     */
    public void example1_HourlyAggregationSingleDevice() {
        // 查询参数
        InfluxdbMeasurement measurement = InfluxdbMeasurement.METER; // 表具测量
        List<String> tags = Arrays.asList("device001");
        InfluxdbField field = InfluxdbField.READING;
        Date startTime = new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000); // 24小时前
        Date endTime = new Date();
        String windowDuration = "1h"; // 1小时窗口
        List<String> groupByTags = null; // 不需要额外分组

        // 执行查询
        List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
                measurement, tags, field, startTime, endTime, windowDuration, groupByTags
        );

        // 处理结果
        results.forEach(result -> {
            System.out.printf("设备: %s, 时间窗口: %s - %s, 聚合值: %.2f%n",
                    result.getTag(),
                    result.getWindowStart(),
                    result.getWindowStop(),
                    result.getAggregatedValue()
            );
        });
    }

    /**
     * 示例2：按天聚合多个设备的读数，并按区域分组
     */
    public void example2_DailyAggregationMultipleDevicesWithRegionGrouping() {
        // 查询参数
        InfluxdbMeasurement measurement = InfluxdbMeasurement.METER;
        List<String> tags = Arrays.asList("device001", "device002", "device003");
        InfluxdbField field = InfluxdbField.READING;
        Date startTime = new Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000); // 7天前
        Date endTime = new Date();
        String windowDuration = "1d"; // 1天窗口
        List<String> groupByTags = Arrays.asList("region", "building"); // 按区域和建筑分组

        // 执行查询
        List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
                measurement, tags, field, startTime, endTime, windowDuration, groupByTags
        );

        // 处理结果
        results.forEach(result -> {
            System.out.printf("设备: %s, 时间窗口: %s, 聚合值: %.2f, 维度: %s%n",
                    result.getTag(),
                    result.getWindowStart(),
                    result.getAggregatedValue(),
                    result.getDimensions()
            );
        });
    }

    /**
     * 示例3：按15分钟聚合所有设备的读数
     */
    public void example3_FifteenMinuteAggregationAllDevices() {
        // 查询参数
        InfluxdbMeasurement measurement = InfluxdbMeasurement.METER;
        List<String> tags = null; // 查询所有设备
        InfluxdbField field = InfluxdbField.READING;
        Date startTime = new Date(System.currentTimeMillis() - 6 * 60 * 60 * 1000); // 6小时前
        Date endTime = new Date();
        String windowDuration = "15m"; // 15分钟窗口
        List<String> groupByTags = null;

        // 执行查询
        List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
                measurement, tags, field, startTime, endTime, windowDuration, groupByTags
        );

        // 处理结果
        results.forEach(result -> {
            System.out.printf("设备: %s, 时间: %s, 15分钟聚合值: %.2f%n",
                    result.getTag(),
                    result.getWindowStart(),
                    result.getAggregatedValue()
            );
        });
    }

    /**
     * 示例4：自定义时间窗口和多维度分组
     */
    public void example4_CustomWindowWithMultipleDimensions() {
        // 查询参数
        InfluxdbMeasurement measurement = InfluxdbMeasurement.METER;
        List<String> tags = Arrays.asList("device001", "device002");
        InfluxdbField field = InfluxdbField.READING;
        Date startTime = new Date(System.currentTimeMillis() - 30 * 24 * 60 * 60 * 1000); // 30天前
        Date endTime = new Date();
        String windowDuration = "6h"; // 6小时窗口
        List<String> groupByTags = Arrays.asList("region", "deviceType", "status"); // 多维度分组

        // 执行查询
        List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
                measurement, tags, field, startTime, endTime, windowDuration, groupByTags
        );

        // 处理结果并按维度分组显示
        results.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                        result -> result.getDimensions().toString()
                ))
                .forEach((dimensions, groupResults) -> {
                    System.out.println("维度组合: " + dimensions);
                    groupResults.forEach(result -> {
                        System.out.printf("  设备: %s, 时间: %s, 聚合值: %.2f%n",
                                result.getTag(),
                                result.getWindowStart(),
                                result.getAggregatedValue()
                        );
                    });
                    System.out.println();
                });
    }
}
