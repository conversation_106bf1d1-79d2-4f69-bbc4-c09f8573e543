package com.jianyou.web.controller.biz;

import com.jianyou.biz.domain.bo.GroupAnalyseBO;
import com.jianyou.biz.domain.vo.EnergyAnalyseVO;
import com.jianyou.biz.domain.vo.EnergyRatioVO;
import com.jianyou.biz.service.IGroupAnalyseService;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.QueryGroup;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 仪表分析控制器
 *
 * <AUTHOR>
 * @date 2025/07/02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/groupAnalyse")
public class GroupAnalyseController {

    private final IGroupAnalyseService groupAnalyseService;

    /**
     * 能耗分析
     */
    @GetMapping("/energyAnalyse")
    public R<EnergyAnalyseVO> energyAnalyse(@Validated(QueryGroup.class) GroupAnalyseBO bo) {
        return R.ok(groupAnalyseService.energyAnalyse(bo));
    }

    /**
     * 能耗占比
     */
    @GetMapping("/energyRatio")
    public R<List<EnergyRatioVO>> energyRatio(@Validated(QueryGroup.class) GroupAnalyseBO bo) {
        return R.ok(groupAnalyseService.energyRatio(bo));
    }

}
