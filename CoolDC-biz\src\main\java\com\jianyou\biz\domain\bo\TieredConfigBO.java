package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 阶梯配置业务对象 enms_tiered_config
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TieredConfigBO extends BaseEntity {

    /**
     * ID
     */
    private String id;

    /**
     * 计费配置ID
     */
    private String billingConfigId;

    /**
     * 阶段
     */
    private Long stage;

    /**
     * 费率
     */
    private BigDecimal rate;

    /**
     * 起始能耗
     */
    private BigDecimal startEnergy;

    /**
     * 截止能耗
     */
    private BigDecimal endEnergy;


}
