package com.jianyou.biz.domain.vo;

import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备库存视图对象 biz_facility_storage
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@ExcelIgnoreUnannotated
public class FacilityStorageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 设备id
     */
    @ExcelProperty(value = "设备id")
    private String facilityId;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String facilityName;

    /**
     * 库存
     */
    @ExcelProperty(value = "库存")
    private BigDecimal stock;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private String projectId;


}
