package com.jianyou.common.utils.influxdb.test;

import cn.hutool.core.date.DateUtil;
import com.jianyou.common.enums.InfluxdbField;
import com.jianyou.common.enums.InfluxdbMeasurement;
import com.jianyou.common.utils.influxdb.InfluxUtils;
import com.jianyou.common.utils.influxdb.domain.dto.WindowAggregationResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 时间开窗聚合测试类
 * 基于您提供的Flux查询语句进行测试
 *
 * <AUTHOR> Assistant
 * @date 2025/01/02
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WindowAggregationTest {

    private final InfluxUtils influxUtils;

    /**
     * 测试方法1：按天聚合体积数据（对应您的Flux查询）
     * 对应的Flux查询：
     * import "timezone"
     * import "math"
     * option location = timezone.location(name: "Asia/Shanghai")
     * from(bucket: "test")
     *   |> range(start: v.timeRangeStart, stop: v.timeRangeStop)
     *   |> filter(fn: (r) => r["_measurement"] == "meterVolume")
     *   |> filter(fn: (r) => r["_field"] == "volume")
     *   |> aggregateWindow(every: 1d, fn: sum, createEmpty: false)
     *   |> yield(name: "daily_sum")
     */
    public void testDailyVolumeAggregation() {
        log.info("开始测试按天聚合体积数据...");
        
        try {
            // 设置查询参数
            InfluxdbMeasurement measurement = InfluxdbMeasurement.METER_VOLUME; // meterVolume
            List<String> tags = null; // 查询所有设备，对应您的查询没有tag过滤
            InfluxdbField field = InfluxdbField.VOLUME; // volume字段
            Date startTime = DateUtil.offsetDay(new Date(), -7); // 7天前
            Date endTime = new Date(); // 现在
            String windowDuration = "1d"; // 1天窗口
            List<String> groupByTags = null; // 无额外分组
            
            // 执行查询
            List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
                    measurement, tags, field, startTime, endTime, windowDuration, groupByTags
            );
            
            // 输出结果
            log.info("查询完成，共获得 {} 条聚合结果", results.size());
            
            if (results.isEmpty()) {
                log.warn("未查询到任何数据，请检查：");
                log.warn("1. bucket配置是否正确");
                log.warn("2. 时间范围内是否有meterVolume测量的volume字段数据");
                log.warn("3. InfluxDB连接是否正常");
                return;
            }
            
            // 按设备分组显示结果
            Map<String, List<WindowAggregationResult>> groupedByDevice = results.stream()
                    .collect(Collectors.groupingBy(WindowAggregationResult::getTag));
            
            groupedByDevice.forEach((deviceTag, deviceResults) -> {
                log.info("=== 设备: {} ===", deviceTag);
                deviceResults.forEach(result -> {
                    log.info("日期: {}, 日聚合体积: {:.2f}", 
                            result.getWindowStart(), 
                            result.getAggregatedValue());
                });
                
                // 计算总体积
                double totalVolume = deviceResults.stream()
                        .mapToDouble(WindowAggregationResult::getAggregatedValue)
                        .sum();
                log.info("设备 {} 总体积: {:.2f}", deviceTag, totalVolume);
                log.info("");
            });
            
        } catch (Exception e) {
            log.error("测试执行失败", e);
        }
    }

    /**
     * 测试方法2：指定设备的按天聚合
     */
    public void testSpecificDeviceDailyAggregation() {
        log.info("开始测试指定设备的按天聚合...");
        
        try {
            // 指定要查询的设备
            List<String> specificDevices = Arrays.asList("device001", "device002", "meter001");
            
            List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
                    InfluxdbMeasurement.METER_VOLUME,
                    specificDevices, // 指定设备
                    InfluxdbField.VOLUME,
                    DateUtil.offsetDay(new Date(), -7),
                    new Date(),
                    "1d",
                    null
            );
            
            log.info("指定设备查询完成，共获得 {} 条结果", results.size());
            
            results.forEach(result -> {
                log.info("设备: {}, 日期: {}, 体积: {:.2f}", 
                        result.getTag(),
                        result.getWindowStart(),
                        result.getAggregatedValue());
            });
            
        } catch (Exception e) {
            log.error("指定设备测试失败", e);
        }
    }

    /**
     * 测试方法3：按小时聚合（更细粒度）
     */
    public void testHourlyVolumeAggregation() {
        log.info("开始测试按小时聚合体积数据...");
        
        try {
            List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
                    InfluxdbMeasurement.METER_VOLUME,
                    null,
                    InfluxdbField.VOLUME,
                    DateUtil.offsetHour(new Date(), -24), // 24小时前
                    new Date(),
                    "1h", // 1小时窗口
                    null
            );
            
            log.info("小时聚合查询完成，共获得 {} 条结果", results.size());
            
            // 只显示前10条结果，避免日志过多
            results.stream()
                    .limit(10)
                    .forEach(result -> {
                        log.info("设备: {}, 时间: {}, 小时体积: {:.2f}", 
                                result.getTag(),
                                result.getWindowStart(),
                                result.getAggregatedValue());
                    });
            
            if (results.size() > 10) {
                log.info("... 还有 {} 条结果未显示", results.size() - 10);
            }
            
        } catch (Exception e) {
            log.error("小时聚合测试失败", e);
        }
    }

    /**
     * 测试方法4：多维度分组聚合
     */
    public void testMultiDimensionAggregation() {
        log.info("开始测试多维度分组聚合...");
        
        try {
            // 假设您的数据中有region和building等标签
            List<String> groupByTags = Arrays.asList("region", "building", "deviceType");
            
            List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
                    InfluxdbMeasurement.METER_VOLUME,
                    null,
                    InfluxdbField.VOLUME,
                    DateUtil.offsetDay(new Date(), -3), // 3天前
                    new Date(),
                    "1d",
                    groupByTags // 多维度分组
            );
            
            log.info("多维度聚合查询完成，共获得 {} 条结果", results.size());
            
            // 按维度组合分组显示
            Map<String, List<WindowAggregationResult>> groupedByDimensions = results.stream()
                    .collect(Collectors.groupingBy(
                            result -> result.getDimensions().toString()
                    ));
            
            groupedByDimensions.forEach((dimensions, groupResults) -> {
                log.info("=== 维度组合: {} ===", dimensions);
                groupResults.forEach(result -> {
                    log.info("设备: {}, 日期: {}, 体积: {:.2f}", 
                            result.getTag(),
                            result.getWindowStart(),
                            result.getAggregatedValue());
                });
                log.info("");
            });
            
        } catch (Exception e) {
            log.error("多维度聚合测试失败", e);
        }
    }

    /**
     * 执行所有测试
     */
    public void runAllTests() {
        log.info("========== 开始执行所有时间开窗聚合测试 ==========");
        
        testDailyVolumeAggregation();
        log.info("---");
        
        testSpecificDeviceDailyAggregation();
        log.info("---");
        
        testHourlyVolumeAggregation();
        log.info("---");
        
        testMultiDimensionAggregation();
        
        log.info("========== 所有测试执行完成 ==========");
    }
}
