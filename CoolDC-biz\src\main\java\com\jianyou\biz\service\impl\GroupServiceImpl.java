package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.GroupDO;
import com.jianyou.biz.domain.bo.GroupBO;
import com.jianyou.biz.domain.vo.GroupVO;
import com.jianyou.biz.mapper.GroupMapper;
import com.jianyou.biz.service.IGroupService;
import com.jianyou.biz.service.IMeterGroupService;
import com.jianyou.common.core.domain.BaseOptionsVO;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 表具分组Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RequiredArgsConstructor
@Service
public class GroupServiceImpl implements IGroupService {

    private final GroupMapper baseMapper;
    private final IMeterGroupService meterGroupService;

    /**
     * 查询表具分组列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link GroupVO }>
     */
    @Override
    public TableDataInfo<GroupVO> queryPageList(GroupBO bo, PageQuery pageQuery){
        IPage<GroupVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<GroupDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getName()), GroupDO::getName, bo.getName())
            .eq(StrUtil.isNotEmpty(bo.getId()), GroupDO::getId, bo.getId())
        );
        return TableDataInfo.build(page);
    }

    /**
     * 查询表具分组
     *
     * @param id id
     * @return {@link GroupVO }
     */
    @Override
    public GroupVO queryById(String id){
        GroupVO groupVO = baseMapper.selectVoById(id);
        if (groupVO != null) {
            // 查询关联的表具ID
            List<String> meterIds = meterGroupService.getMeterIdsByGroupId(id);
            groupVO.setMeterIds(meterIds);
        }
        return groupVO;
    }

    /**
     * 新增表具分组
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(GroupBO bo){
        // 1. 插入分组基本信息
        GroupDO groupDO = new GroupDO();
        BeanCopyUtils.copy(bo, groupDO);
        boolean result = baseMapper.insert(groupDO) > 0;

        // 2. 插入表具与分组的关联关系
        if (result && CollUtil.isNotEmpty(bo.getMeterIds())) {
            meterGroupService.saveBatch(groupDO.getId(), bo.getMeterIds());
        }

        return result;
    }

    /**
     * 修改表具分组
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(GroupBO bo){
        GroupDO groupDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(groupDO)) {
            return false;
        }

        // 1. 更新分组基本信息
        BeanCopyUtils.copy(bo, groupDO);
        boolean result = baseMapper.updateById(groupDO) > 0;

        // 2. 更新表具关联关系
        if (result && bo.getMeterIds() != null) {
            meterGroupService.saveBatch(bo.getId(), bo.getMeterIds());
        }

        return result;
    }

    /**
     * 校验并批量删除表具分组信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid){
        if (CollUtil.isEmpty(ids)) {
            return false;
        }

        // 1. 删除表具与分组的关联关系
        for (String id : ids) {
            meterGroupService.deleteByGroupId(id);
        }

        // 2. 删除分组
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<BaseOptionsVO> option() {
        // 查询所有分组的基础信息（仅包含 id 和 name）
        List<GroupDO> groupList = baseMapper.selectList(Wrappers.<GroupDO>lambdaQuery()
            .select(GroupDO::getId, GroupDO::getName));

        if (CollUtil.isEmpty(groupList)) {
            return Collections.emptyList();
        }

        // 转换为 BaseOptionsVO 列表
        return groupList.stream()
            .map(group -> new BaseOptionsVO(group.getId(), group.getName()))
            .collect(Collectors.toList());
    }

}
