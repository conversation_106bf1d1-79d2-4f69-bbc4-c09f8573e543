package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 计划变更业务对象 biz_plan_change
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PlanChangeBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 施工计划id
     */
    @NotBlank(message = "施工计划id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String buildPlanId;

    /**
     * 变更事由
     */
    @NotBlank(message = "变更事由不能为空", groups = { AddGroup.class, EditGroup.class })
    private String changeReason;

    /**
     * 变更单（图片）
     */
    @NotBlank(message = "变更单（图片）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ossIds;

    /**
     * 变更状态（未确认、确认、否认、废除）
     */
    @NotBlank(message = "变更状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String changeStatus;

    /**
     * 变更时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "变更时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date changeTime;

    /**
     * 项目id
     */
    @NotBlank(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectId;


}
