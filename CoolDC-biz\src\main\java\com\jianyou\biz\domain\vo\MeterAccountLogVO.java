package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jianyou.common.annotation.ExcelDictFormat;
import com.jianyou.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 预存账户流水视图对象 enms_meter_account_log
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@ExcelIgnoreUnannotated
public class MeterAccountLogVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 表具id
     */
    @ExcelProperty(value = "表具id")
    private String meterId;

    /**
     * 变动金额（正数=充值，负数=扣费）
     */
    @ExcelProperty(value = "变动金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "正=数=充值，负数=扣费")
    private BigDecimal changeAmount;

    /**
     * 类型：1=充值，2=扣费，3=退费，4=手工调整
     */
    @ExcelProperty(value = "类型：1=充值，2=扣费，3=退费，4=手工调整")
    private String type;

    /**
     * 变动前余额
     */
    @ExcelProperty(value = "变动前余额")
    private BigDecimal beforeBalance;

    /**
     * 变动后余额
     */
    @ExcelProperty(value = "变动后余额")
    private BigDecimal afterBalance;

    /**
     * 变动时间
     */
    @ExcelProperty(value = "变动时间")
    private Date createdTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
