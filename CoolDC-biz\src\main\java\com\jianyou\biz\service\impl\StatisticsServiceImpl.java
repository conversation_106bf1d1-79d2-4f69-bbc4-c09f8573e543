package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.MeterDO;
import com.jianyou.biz.domain.bo.EnergyStatisticBO;
import com.jianyou.biz.domain.vo.EnergyStatisticVO;
import com.jianyou.biz.domain.vo.MeterVO;
import com.jianyou.biz.mapper.AreaMapper;
import com.jianyou.biz.mapper.MeterEnergyMapper;
import com.jianyou.biz.mapper.MeterMapper;
import com.jianyou.biz.service.IStatisticsService;
import com.jianyou.biz.utils.EnergyAnalyseUtils;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.enums.AnalysisType;
import com.jianyou.common.utils.TimeFormatUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 统计服务
 *
 * <AUTHOR>
 * @date 2025/07/07
 */
@Service
@RequiredArgsConstructor
public class StatisticsServiceImpl implements IStatisticsService {

    private final MeterMapper meterMapper;
    private final AreaMapper areaMapper;
    private final MeterEnergyMapper meterEnergyMapper;

    /**
     * 能耗统计
     */
    @Override
    public TableDataInfo<EnergyStatisticVO> statisticsEnergy(EnergyStatisticBO bo, PageQuery pageQuery, boolean isPage) {
        List<MeterVO> records;
        long total = 0;
        LambdaQueryWrapper<MeterDO> wrapper = Wrappers.<MeterDO>lambdaQuery()
            .in(MeterDO::getId, bo.getMeterIds());
        if (isPage) {
            IPage<MeterVO> page = meterMapper.selectVoPage(pageQuery.build(), wrapper);
            records = page.getRecords();
            total = page.getTotal();
        } else {
            records = meterMapper.selectVoList(wrapper);
            total = records.size();
        }
        if (CollUtil.isEmpty(records)) {
            return new TableDataInfo<>();
        }

        AnalysisType analysisType = AnalysisType.getNameByValue(bo.getAnalysisType());
        String startTime = bo.getStartTime();
        String endTime = bo.getEndTime();
        Date[] range = TimeFormatUtil.formatRange(startTime, endTime, analysisType, false);

        List<EnergyStatisticVO> vos = records.stream().map(record -> {
            EnergyStatisticVO vo = new EnergyStatisticVO();
            vo.setMeterId(record.getId());
            vo.setMeterName(record.getName());
            vo.setMeterNumber(record.getNumber());

            // 查询区域名称
            String areaFullPath = areaMapper.getAreaFullPath(record.getAreaId());
            vo.setAreaName(areaFullPath);

            // 使用meterEnergyMapper查询单个表具的能耗数据
            Map<String, Object> energyData = EnergyAnalyseUtils.collectSingleMeterEnergyFromDB(
                meterEnergyMapper, record.getNumber(), analysisType, range);

            // 保留两位小数
            energyData.forEach((k, v) -> {
                if (ObjUtil.isEmpty(v)) {
                    return;
                }
                energyData.put(k, new BigDecimal(v.toString()));
            });
            vo.setEnergyData(energyData);
            return vo;
        }).collect(Collectors.toList());
        return TableDataInfo.build(vos, total);
    }


}
