package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 阶梯配置对象 enms_tiered_config
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@TableName("enms_tiered_config")
public class TieredConfigDO {

    /**
     * ID
     */
    private String id;
    /**
     * 计费配置ID
     */
    private String billingConfigId;
    /**
     * 阶段
     */
    private Long stage;
    /**
     * 费率
     */
    private BigDecimal rate;
    /**
     * 起始能耗
     */
    private BigDecimal startEnergy;
    /**
     * 截止能耗
     */
    private BigDecimal endEnergy;

}
