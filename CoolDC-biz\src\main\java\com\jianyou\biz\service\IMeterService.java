package com.jianyou.biz.service;

import cn.hutool.core.lang.tree.Tree;
import com.jianyou.biz.domain.bo.FeeBO;
import com.jianyou.biz.domain.bo.MeterBO;
import com.jianyou.biz.domain.vo.MeterVO;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 表具Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IMeterService {


    /**
     * 查询表具列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @param isPage  是否分页
     * @return {@link TableDataInfo }<{@link MeterVO }>
     */
    TableDataInfo<MeterVO> queryPageList(MeterBO bo, PageQuery pageQuery, boolean isPage);

    /**
     * 查询表具
     *
     * @param id id
     * @return {@link MeterVO }
     */
    MeterVO queryById(String id);

    /**
     * 新增表具
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(MeterBO bo);

    /**
     * 修改表具
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(MeterBO bo);

    /**
     * 校验并批量删除表具信息
     *
     * @param ids     主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 查询区域表具树
     *
     * @return 区域表具树结构
     */
    List<Tree<String>> queryAreaMeterTree(String energyType);

    /**
     * 预存
     */
    Boolean topUp(FeeBO bo);

    /**
     * 退费
     */
    Boolean returnFee(FeeBO bo);

    /**
     * 修改自动退费
     */
    Boolean editAutoPay(FeeBO bo);

    /**
     * 抄表
     *
     * @param meterIds   仪表id
     * @param readingWay 抄表方式 1手动 0自动
     * @param isBilling  是否计费
     */
    Boolean reading(List<String> meterIds, String readingWay, boolean isBilling);
}
