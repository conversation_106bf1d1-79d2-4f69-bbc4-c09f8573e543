package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 抄表记录对象 enms_meter_reading
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@TableName("enms_meter_reading")
public class MeterReadingDO {

    /**
     * ID
     */
    private String id;
    /**
     * 表具id
     */
    private String meterId;
    /**
     * 表具名称
     */
    private String meterName;
    /**
     * 表具编号
     */
    private String meterNumber;
    /**
     * 房号
     */
    private String roomNumber;
    /**
     * 能源类型
     */
    private String energyType;
    /**
     * 表具读数
     */
    private BigDecimal meterRecord;
    /**
     * 表具时间
     */
    private String meterTime;
    /**
     * 抄表时间
     */
    private Date readingTime;
    /**
     * 抄表方式(0自动;1手动)
     */
    private String readingWay;

}
