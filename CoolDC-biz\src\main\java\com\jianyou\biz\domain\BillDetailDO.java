package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 账单详情对象 enms_bill_detail
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@TableName("enms_bill_detail")
public class BillDetailDO  {


    /**
     * ID
     */
    private String id;
    /**
     * 账单id
     */
    private String billId;
    /**
     * 阶段名
     */
    private String phase;
    /**
     * 阶梯上限
     */
    private BigDecimal maxVolume;
    /**
     * 峰/平/谷
     */
    private String timeType;
    /**
     * 实际时间范围
     */
    private String timeRange;
    /**
     * 能耗量
     */
    private BigDecimal volume;
    /**
     * 能源单位
     */
    private String energyUnit;
    /**
     * 基价
     */
    private BigDecimal basePrice;
    /**
     * 加价（峰/谷加价）
     */
    private BigDecimal addPrice;
    /**
     * 单价（基价+加价）
     */
    private BigDecimal unitPrice;
    /**
     * 费用
     */
    private BigDecimal fee;

}
