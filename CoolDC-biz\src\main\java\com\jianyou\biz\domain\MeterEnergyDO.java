package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 具能耗用量对象 enms_meter_energy
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@TableName("enms_meter_energy")
public class MeterEnergyDO {

    /**
     * ID
     */
    private String id;
    /**
     * 表具编号
     */
    private String meterNumber;
    /**
     * 能耗用量
     */
    private BigDecimal volume;
    /**
     * 小时
     */
    private Date hour;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
