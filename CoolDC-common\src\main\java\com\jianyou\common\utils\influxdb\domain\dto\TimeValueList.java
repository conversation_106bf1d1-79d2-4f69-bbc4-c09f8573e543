package com.jianyou.common.utils.influxdb.domain.dto;

import lombok.Data;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 时间值列表
 *
 * <AUTHOR>
 * @date 2025/07/07
 */
@Data
public class TimeValueList {
    private List<TimeValue> timeValues;

    public TimeValueList(List<TimeValue> timeValues) {
        // 在构造函数里自动做时间升序排序，保证后面用到的列表永远是有序的
        this.timeValues = timeValues.stream()
            .sorted(Comparator.comparing(TimeValue::getTime))
            .collect(Collectors.toList());
    }

    /**
     * 二分查找：找到时间点 time 之前或等于 time 的最大时间对应的值 找不到则为空
     */
    public TimeValue findNearestValue(Date time) {
        int left = 0, right = timeValues.size() - 1;
        TimeValue result = null;

        while (left <= right) {
            int mid = left + (right - left) / 2;
            Date midDate = timeValues.get(mid).getTime();
            if (!midDate.after(time)) {
                result = timeValues.get(mid);
                // 设置 index 字段
                result.setIndex(mid);
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }

        return result;
    }
}
