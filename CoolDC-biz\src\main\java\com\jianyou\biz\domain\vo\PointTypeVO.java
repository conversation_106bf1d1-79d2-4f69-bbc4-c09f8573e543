package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 点位类型视图对象 biz_point_type
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@ExcelIgnoreUnannotated
public class PointTypeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 字段名称
     */
    @ExcelProperty(value = "字段名称")
    private String fieldName;


}
