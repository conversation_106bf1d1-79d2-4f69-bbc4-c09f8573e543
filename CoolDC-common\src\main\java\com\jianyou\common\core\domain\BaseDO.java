package com.jianyou.common.core.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jianyou.common.helper.LoginHelper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * DO父类
 *
 * <AUTHOR>
 * @Date 2023/7/13
 **/
@Data
public class BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 创建
     */
    public void create() {
        this.setDelFlag('0');
        this.setCreateBy(String.valueOf(LoginHelper.getUserId()));
        this.setCreateTime(new Date());
    }

    /**
     * 修改
     */
    public void update() {
        this.setUpdateBy(String.valueOf(LoginHelper.getUserId()));
        this.setUpdateTime(new Date());
    }
    /**
     * 删除
     */
    public void delete() {
        this.setDelFlag('1');
        this.setUpdateBy(String.valueOf(LoginHelper.getUserId()));
        this.setUpdateTime(new Date());
    }
    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标志  0存在  1删除
     */
    @TableLogic(value="0",delval="1")
    private Character delFlag;

}
