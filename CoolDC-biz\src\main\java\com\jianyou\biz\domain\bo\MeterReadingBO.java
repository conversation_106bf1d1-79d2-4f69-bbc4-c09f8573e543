package com.jianyou.biz.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 抄表记录业务对象 enms_meter_reading
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MeterReadingBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String id;

    /**
     * 表具id
     */
    @NotBlank(message = "表具id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String meterId;

    /**
     * 表具名称
     */
    @NotBlank(message = "表具名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String meterName;

    /**
     * 表具编号
     */
    @NotBlank(message = "表具编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String meterNumber;

    /**
     * 房号
     */
    @NotBlank(message = "房号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String roomNumber;

    /**
     * 能源类型
     */
    @NotBlank(message = "能源类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String energyType;

    /**
     * 表具读数
     */
    private BigDecimal meterRecord;

    /**
     * 表具时间
     */
    @NotBlank(message = "表具时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String meterTime;

    /**
     * 抄表时间
     */
    @NotNull(message = "抄表时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date readingTime;

    /**
     * 抄表方式(0自动;1手动)
     */
    @NotBlank(message = "抄表方式(0自动;1手动)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String readingWay;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startReadingTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endReadingTime;


}
