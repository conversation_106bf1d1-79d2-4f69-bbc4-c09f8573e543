package com.jianyou.common.utils.influxdb.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 时间值
 *
 * <AUTHOR>
 * @date 2025/07/07
 */
@Data
@AllArgsConstructor
public class TimeValue {
    private Date time;
    private BigDecimal value;
    private Integer index;

    public TimeValue(Date time, BigDecimal value){
        this.time = time;
        this.value = value;
    }
}
