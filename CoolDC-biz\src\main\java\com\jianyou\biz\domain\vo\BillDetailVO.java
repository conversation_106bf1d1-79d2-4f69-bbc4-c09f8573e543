package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jianyou.common.annotation.ExcelDictFormat;
import com.jianyou.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 账单详情视图对象 enms_bill_detail
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@ExcelIgnoreUnannotated
public class BillDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 账单id
     */
    @ExcelProperty(value = "账单id")
    private String billId;

    /**
     * 阶段名
     */
    @ExcelProperty(value = "阶段名")
    private String phase;

    /**
     * 阶梯上限
     */
    private BigDecimal maxVolume;

    /**
     * 峰/平/谷
     */
    @ExcelProperty(value = "峰/平/谷")
    private String timeType;

    /**
     * 实际时间范围
     */
    @ExcelProperty(value = "实际时间范围")
    private String timeRange;

    /**
     * 能耗量
     */
    @ExcelProperty(value = "能耗量")
    private BigDecimal volume;
    /**
     * 能源单位
     */
    private String energyUnit;
    /**
     * 基价
     */
    private BigDecimal basePrice;
    /**
     * 加价（峰/谷加价）
     */
    private BigDecimal addPrice;
    /**
     * 单价（基价+加价）
     */
    @ExcelProperty(value = "单价", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "基=价+加价")
    private BigDecimal unitPrice;

    /**
     * 费用
     */
    @ExcelProperty(value = "费用")
    private BigDecimal fee;


}
