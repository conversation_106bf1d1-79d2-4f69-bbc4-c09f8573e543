package ${packageName}.service;

import ${packageName}.domain.vo.${ClassName}VO;
import ${packageName}.domain.bo.${ClassName}BO;
#if($table.crud || $table.sub)
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;
#end

import java.util.Collection;

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service {


#if($table.crud || $table.sub)
    /**
     * 查询${functionName}列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link ${ClassName}VO }>
     */
    TableDataInfo<${ClassName}VO> queryPageList(${ClassName}BO bo, PageQuery pageQuery);
#end

    /**
     * 查询${functionName}
     *
     * @param id id
     * @return {@link ${ClassName}VO }
     */
     ${ClassName}VO queryById(${pkColumn.javaType} ${pkColumn.javaField});

    /**
     * 新增${functionName}
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(${ClassName}BO bo);

    /**
     * 修改${functionName}
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(${ClassName}BO bo);

    /**
     * 校验并批量删除${functionName}信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<${pkColumn.javaType}> ids, Boolean isValid);
}
