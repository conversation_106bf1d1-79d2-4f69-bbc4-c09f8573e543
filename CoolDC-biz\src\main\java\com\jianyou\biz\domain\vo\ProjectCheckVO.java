package com.jianyou.biz.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jianyou.common.annotation.ExcelDictFormat;
import com.jianyou.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;

/**
 * 项目验收视图对象 biz_project_check
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
public class ProjectCheckVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 验收报告（上传附件）
     */
    @ExcelProperty(value = "验收报告", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "上=传附件")
    private String reportOssIds;

    /**
     * 验收资料（图纸、单据）
     */
    @ExcelProperty(value = "验收资料", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "图=纸、单据")
    private String dataOssIds;

    /**
     * 验收人
     */
    @ExcelProperty(value = "验收人")
    private String acceptor;

    /**
     * 验收时间
     */
    @ExcelProperty(value = "验收时间")
    private Date receiveTime;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private String projectId;
    private String projectName;


}
