package com.jianyou.biz.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备请领视图对象 biz_facility_get_storage
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@ExcelIgnoreUnannotated
public class FacilityGetStorageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 请领人
     */
    @ExcelProperty(value = "请领人")
    private String getPrincipal;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String details;

    /**
     * 请领时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "请领时间")
    private Date drawTime;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private String projectId;


}
