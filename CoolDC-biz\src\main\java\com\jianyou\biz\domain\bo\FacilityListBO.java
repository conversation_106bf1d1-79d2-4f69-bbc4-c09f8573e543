package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 设备清单业务对象 biz_facility_list
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FacilityListBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 编号
     */
    @NotBlank(message = "编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String number;

    /**
     * 规格型号
     */
    @NotBlank(message = "规格型号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String model;

    /**
     * 设备状态（使用中、备用、被更换)
     */
    @NotBlank(message = "设备状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String facilityStatus;

    /**
     * 厂家名称
     */
    @NotBlank(message = "厂家名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String factoryName;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String linkman;

    /**
     * 联系人电话
     */
    @NotBlank(message = "联系人电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String linkmanNumber;

    /**
     * 项目id
     */
    @NotBlank(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectId;


}
