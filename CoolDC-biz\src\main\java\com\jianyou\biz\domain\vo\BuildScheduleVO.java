package com.jianyou.biz.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 施工进度视图对象 biz_build_schedule
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@ExcelIgnoreUnannotated
public class BuildScheduleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 施工计划id
     */
    @ExcelProperty(value = "施工计划id")
    private String buildPlanId;
    private String buildPlanName;

    /**
     * 今日工作
     */
    @ExcelProperty(value = "今日工作")
    private String todayWork;

    /**
     * 计划工作
     */
    @ExcelProperty(value = "计划工作")
    private String planWork;

    /**
     * 现场照片
     */
    @ExcelProperty(value = "现场照片")
    private String ossIds;

    /**
     * 项目id
     */
    @ExcelProperty(value = "项目id")
    private String projectId;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "上报时间")
    private Date reportTime;


}
