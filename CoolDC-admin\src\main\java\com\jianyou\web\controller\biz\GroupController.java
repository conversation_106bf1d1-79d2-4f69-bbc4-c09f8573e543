package com.jianyou.web.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jianyou.biz.domain.bo.GroupBO;
import com.jianyou.biz.domain.vo.GroupVO;
import com.jianyou.biz.service.IGroupService;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.BaseOptionsVO;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 表具分组
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/group")
public class GroupController extends BaseController {

    private final IGroupService groupService;

    /**
     * 查询表具分组列表
     */
    @SaCheckPermission("biz:group:list")
    @GetMapping("/list")
    public TableDataInfo<GroupVO> list(GroupBO bo, PageQuery pageQuery) {
        return groupService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取表具分组详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:group:query")
    @GetMapping("/{id}")
    public R<GroupVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(groupService.queryById(id));
    }

    /**
     * 新增表具分组
     */
    @SaCheckPermission("biz:group:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GroupBO bo) {
        return toAjax(groupService.insertByBo(bo));
    }

    /**
     * 修改表具分组
     */
    @SaCheckPermission("biz:group:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GroupBO bo) {
        return toAjax(groupService.updateByBo(bo));
    }

    /**
     * 删除表具分组
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:group:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(groupService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 查询表具分组选项
     */
    @GetMapping("/option")
    public R<List<BaseOptionsVO>> option() {
        return R.ok(groupService.option());
    }
}
