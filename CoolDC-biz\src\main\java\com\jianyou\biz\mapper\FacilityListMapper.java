package com.jianyou.biz.mapper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.FacilityListDO;
import com.jianyou.biz.domain.vo.FacilityListVO;
import com.jianyou.common.core.mapper.BaseMapperPlus;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备清单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface FacilityListMapper extends BaseMapperPlus<FacilityListMapper, FacilityListDO, FacilityListVO> {

    default Map<String, FacilityListDO> nameDict(List<String> ids) {
        List<FacilityListDO> list = selectList(Wrappers.<FacilityListDO>lambdaQuery()
            .in(CollUtil.isNotEmpty(ids), FacilityListDO::getId, ids)
        );
        return list.stream().collect(Collectors.toMap(FacilityListDO::getId, facilityListDO -> facilityListDO));
    }
}
