package com.jianyou.web.controller.biz;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.jianyou.common.core.domain.BaseOptionsVO;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import com.jianyou.common.utils.poi.ExcelUtil;
import com.jianyou.biz.domain.vo.PointTypeVO;
import com.jianyou.biz.domain.bo.PointTypeBO;
import com.jianyou.biz.service.IPointTypeService;
import com.jianyou.common.core.page.TableDataInfo;

/**
 * 点位类型
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/pointType")
public class PointTypeController extends BaseController {

    private final IPointTypeService pointTypeService;

    /**
     * 查询点位类型列表
     */
    @SaCheckPermission("biz:pointType:list")
    @GetMapping("/list")
    public TableDataInfo<PointTypeVO> list(PointTypeBO bo, PageQuery pageQuery) {
        return pointTypeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出点位类型列表
     */
    @SaCheckPermission("biz:pointType:export")
    @PostMapping("/export")
    public void export(PointTypeBO bo, HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "点位类型", PointTypeVO.class, response);
    }

    /**
     * 获取点位类型详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:pointType:query")
    @GetMapping("/{id}")
    public R<PointTypeVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(pointTypeService.queryById(id));
    }

    /**
     * 新增点位类型
     */
    @SaCheckPermission("biz:pointType:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PointTypeBO bo) {
        return toAjax(pointTypeService.insertByBo(bo));
    }

    /**
     * 修改点位类型
     */
    @SaCheckPermission("biz:pointType:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PointTypeBO bo) {
        return toAjax(pointTypeService.updateByBo(bo));
    }

    /**
     * 删除点位类型
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:pointType:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(pointTypeService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 点位类型选项
     */
    @GetMapping("/option")
    public R<List<BaseOptionsVO>> option() {
        return R.ok(pointTypeService.option());
    }
}
