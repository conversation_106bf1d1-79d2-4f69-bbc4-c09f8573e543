package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 项目信息业务对象 biz_project
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 建设单位id
     */
    @NotNull(message = "建设单位不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;

    /**
     * 项目地址
     */
    @NotBlank(message = "项目地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String address;

    /**
     * 项目概括
     */
    @NotBlank(message = "项目概括不能为空", groups = { AddGroup.class, EditGroup.class })
    private String detail;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contacts;

    /**
     * 联系人电话
     */
    @NotBlank(message = "联系人电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contactsNumber;

    /**
     * 施工开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "施工开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 施工截止时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "施工截止时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endTime;


}
