package com.jianyou.biz.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.FacilityStorageDO;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.jianyou.biz.mapper.FacilityStorageMapper;
import com.jianyou.biz.service.IFacilityStorageService;
import com.jianyou.biz.domain.vo.FacilityStorageVO;
import com.jianyou.biz.domain.bo.FacilityStorageBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 设备库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@RequiredArgsConstructor
@Service
public class FacilityStorageServiceImpl implements IFacilityStorageService {

    private final FacilityStorageMapper baseMapper;

    /**
     * 查询设备库存列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link FacilityStorageVO }>
     */
    @Override
    public TableDataInfo<FacilityStorageVO> queryPageList(FacilityStorageBO bo, PageQuery pageQuery) {
        IPage<FacilityStorageVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<FacilityStorageDO>lambdaQuery()
            .eq(StrUtil.isNotEmpty(bo.getProjectId()), FacilityStorageDO::getProjectId, bo.getProjectId())
        );
        return TableDataInfo.build(page);
    }

    /**
     * 查询设备库存列表All
     */
    @Override
    public List<FacilityStorageVO> listAll(FacilityStorageBO bo) {
        return baseMapper.selectVoList(Wrappers.<FacilityStorageDO>lambdaQuery()
            .eq(StrUtil.isNotEmpty(bo.getProjectId()), FacilityStorageDO::getProjectId, bo.getProjectId())
        );
    }

    /**
     * 查询设备库存
     *
     * @param id id
     * @return {@link FacilityStorageVO }
     */
    @Override
    public FacilityStorageVO queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增设备库存
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean insertByBo(FacilityStorageBO bo) {
        FacilityStorageDO facilityStorageDO = new FacilityStorageDO();
        BeanCopyUtils.copy(bo, facilityStorageDO);
        facilityStorageDO.create();
        facilityStorageDO.update();
        return baseMapper.insert(facilityStorageDO) > 0;
    }

    /**
     * 修改设备库存
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(FacilityStorageBO bo) {
        FacilityStorageDO facilityStorageDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(facilityStorageDO)) {
            return false;
        }
        BeanCopyUtils.copy(bo, facilityStorageDO);
        facilityStorageDO.update();
        return baseMapper.updateById(facilityStorageDO) > 0;
    }

    /**
     * 校验并批量删除设备库存信息
     *
     * @param ids     主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 通用库存更新方法
     *
     * @param facilityId   设备ID
     * @param projectId    项目ID
     * @param stockChange  库存变化量（正数为入库，负数为出库）
     * @param facilityName 设备名称（可选，为空时自动查询）
     * @return {@link Boolean} 更新是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStock(String facilityId, String projectId, BigDecimal stockChange, String facilityName) {
        if (ObjUtil.isEmpty(facilityId) || ObjUtil.isEmpty(projectId) || ObjUtil.isEmpty(stockChange)) {
            return false;
        }

        // 查询现有库存记录
        FacilityStorageDO existingStorage = baseMapper.selectOne(Wrappers.<FacilityStorageDO>lambdaQuery()
            .eq(FacilityStorageDO::getFacilityId, facilityId)
            .eq(FacilityStorageDO::getProjectId, projectId)
            .last("LIMIT 1"));

        if (ObjUtil.isNotEmpty(existingStorage)) {
            // 更新现有库存
            BigDecimal newStock = existingStorage.getStock().add(stockChange);
            if (newStock.compareTo(BigDecimal.ZERO) < 0) {
                // 库存不足，返回失败
                return false;
            }
            existingStorage.setStock(newStock);
            existingStorage.update();
            return baseMapper.updateById(existingStorage) > 0;
        } else {
            // 创建新的库存记录（仅当stockChange为正数时）
            if (stockChange.compareTo(BigDecimal.ZERO) <= 0) {
                return false;
            }
            FacilityStorageDO newStorage = new FacilityStorageDO();
            newStorage.setFacilityId(facilityId);
            newStorage.setProjectId(projectId);
            newStorage.setStock(stockChange);
            newStorage.setFacilityName(facilityName);
            newStorage.create();
            newStorage.update();
            return baseMapper.insert(newStorage) > 0;
        }
    }

}
