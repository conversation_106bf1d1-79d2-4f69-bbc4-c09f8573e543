package com.jianyou.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.PeakValleyConfigDO;
import com.jianyou.biz.domain.bo.PeakValleyConfigBO;
import com.jianyou.biz.domain.vo.PeakValleyConfigVO;
import com.jianyou.biz.mapper.PeakValleyConfigMapper;
import com.jianyou.biz.service.IPeakValleyConfigService;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 分段配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RequiredArgsConstructor
@Service
public class PeakValleyConfigServiceImpl implements IPeakValleyConfigService {

    private final PeakValleyConfigMapper baseMapper;

    /**
     * 根据计费配置ID查询分段配置列表
     *
     * @param billingConfigId 计费配置ID
     * @return 分段配置列表
     */
    @Override
    public List<PeakValleyConfigVO> queryByBillingConfigId(String billingConfigId) {
        LambdaQueryWrapper<PeakValleyConfigDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PeakValleyConfigDO::getBillingConfigId, billingConfigId);
        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 批量新增分段配置
     *
     * @param peakValleyConfigs 分段配置列表
     * @return 结果
     */
    @Override
    public boolean insertBatch(List<PeakValleyConfigBO> peakValleyConfigs) {
        if (peakValleyConfigs == null || peakValleyConfigs.isEmpty()) {
            return true;
        }

        List<PeakValleyConfigDO> ladderConfigDOList = new ArrayList<>();
        for (PeakValleyConfigBO ladderConfigBO : peakValleyConfigs) {
            PeakValleyConfigDO ladderConfigDO = new PeakValleyConfigDO();
            BeanCopyUtils.copy(ladderConfigBO, ladderConfigDO);
            ladderConfigDOList.add(ladderConfigDO);
        }

        return baseMapper.insertBatch(ladderConfigDOList);
    }

    /**
     * 根据计费配置ID删除分段配置
     *
     * @param billingConfigId 计费配置ID
     * @return 结果
     */
    @Override
    public boolean deleteByBillingConfigId(String billingConfigId) {
        LambdaQueryWrapper<PeakValleyConfigDO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PeakValleyConfigDO::getBillingConfigId, billingConfigId);
        return baseMapper.delete(wrapper) >= 0;
    }
}
