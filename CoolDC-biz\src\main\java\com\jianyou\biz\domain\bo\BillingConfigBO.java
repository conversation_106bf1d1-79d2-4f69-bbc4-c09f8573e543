package com.jianyou.biz.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 计费配置业务对象 enms_billing_config
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BillingConfigBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = {EditGroup.class})
    private String id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 能源类型
     */
    @NotBlank(message = "能源类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String energyType;

    /**
     * 费率类型 0标准计费 1阶梯计费
     */
    @NotBlank(message = "费率类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String rateType;

    /**
     * 是否分段计费 峰平谷(1是0否)
     */
    @NotBlank(message = "是否分段不能为空", groups = {AddGroup.class, EditGroup.class})
    private String peakValleyFlag;

    /**
     * 标准单价 0标准计费时存在
     */
    private BigDecimal standardPrice;

    /**
     * 单位
     */
    @NotBlank(message = "单位不能为空", groups = {AddGroup.class, EditGroup.class})
    private String unit;

    /**
     * 起始有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "起始有效期不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date startTime;

    /**
     * 截止有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "截止有效期不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date endTime;

    /**
     * 分段配置列表 peakValleyFlag为1时存在
     */
    private List<PeakValleyConfigBO> peakValleyConfigs;

    /**
     * 阶段配置列表 1阶梯计费时存在
     */
    private List<TieredConfigBO> tieredConfigs;

}
