package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jianyou.common.annotation.ExcelDictFormat;
import com.jianyou.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 抄表记录视图对象 enms_meter_reading
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@ExcelIgnoreUnannotated
public class MeterReadingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private String id;

    /**
     * 表具id
     */
    private String meterId;

    /**
     * 表具名称
     */
    @ExcelProperty(value = "表具名称")
    private String meterName;

    /**
     * 表具编号
     */
    @ExcelProperty(value = "表具编号")
    private String meterNumber;

    /**
     * 房号
     */
    @ExcelProperty(value = "房号")
    private String roomNumber;

    /**
     * 能源类型
     */
    @ExcelProperty(value = "能源类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "energy_type")
    private String energyType;

    /**
     * 表具读数
     */
    @ExcelProperty(value = "表具读数")
    private BigDecimal meterRecord;

    /**
     * 表具时间
     */
    @ExcelProperty(value = "表具时间")
    private String meterTime;

    /**
     * 抄表时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "抄表时间")
    private Date readingTime;

    /**
     * 抄表方式(0自动;1手动)
     */
    @ExcelProperty(value = "抄表方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "reading_way")
    private String readingWay;


}
