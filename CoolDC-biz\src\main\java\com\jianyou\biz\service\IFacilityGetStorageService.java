package com.jianyou.biz.service;

import com.jianyou.biz.domain.vo.FacilityGetStorageVO;
import com.jianyou.biz.domain.bo.FacilityGetStorageBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.Collection;

/**
 * 设备请领Service接口
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface IFacilityGetStorageService {


    /**
     * 查询设备请领列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link FacilityGetStorageVO }>
     */
    TableDataInfo<FacilityGetStorageVO> queryPageList(FacilityGetStorageBO bo, PageQuery pageQuery);

    /**
     * 查询设备请领
     *
     * @param id id
     * @return {@link FacilityGetStorageVO }
     */
     FacilityGetStorageVO queryById(String id);

    /**
     * 新增设备请领
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(FacilityGetStorageBO bo);

    /**
     * 修改设备请领
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(FacilityGetStorageBO bo);

    /**
     * 校验并批量删除设备请领信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
