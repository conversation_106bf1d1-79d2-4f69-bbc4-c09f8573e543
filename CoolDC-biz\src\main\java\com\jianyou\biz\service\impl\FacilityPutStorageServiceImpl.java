package com.jianyou.biz.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.FacilityListDO;
import com.jianyou.biz.domain.FacilityPutStorageDO;
import com.jianyou.biz.mapper.FacilityListMapper;
import com.jianyou.biz.mapper.FacilityPutStorageMapper;
import com.jianyou.biz.service.IFacilityStorageService;
import com.jianyou.common.exception.ServiceException;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.jianyou.biz.service.IFacilityPutStorageService;
import com.jianyou.biz.domain.vo.FacilityPutStorageVO;
import com.jianyou.biz.domain.bo.FacilityPutStorageBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 设备入库记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@RequiredArgsConstructor
@Service
public class FacilityPutStorageServiceImpl implements IFacilityPutStorageService {

    private final FacilityPutStorageMapper baseMapper;
    private final FacilityListMapper facilityListMapper;
    private final IFacilityStorageService facilityStorageService;

    /**
     * 查询设备入库记录列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link FacilityPutStorageVO }>
     */
    @Override
    public TableDataInfo<FacilityPutStorageVO> queryPageList(FacilityPutStorageBO bo, PageQuery pageQuery){
        IPage<FacilityPutStorageVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<FacilityPutStorageDO>lambdaQuery()
            .eq(StrUtil.isNotEmpty(bo.getFacilityId()), FacilityPutStorageDO::getFacilityId, bo.getFacilityId())
            .eq(StrUtil.isNotEmpty(bo.getProjectId()), FacilityPutStorageDO::getProjectId, bo.getProjectId())
            .orderByDesc(FacilityPutStorageDO::getEntryTime)
        );
        return TableDataInfo.build(page);
    }

    /**
     * 查询设备入库记录
     *
     * @param id id
     * @return {@link FacilityPutStorageVO }
     */
    @Override
    public FacilityPutStorageVO queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增设备入库记录
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(FacilityPutStorageBO bo){
        FacilityPutStorageDO facilityPutStorageDO = new FacilityPutStorageDO();
        BeanCopyUtils.copy(bo,facilityPutStorageDO);
        facilityPutStorageDO.create();
        facilityPutStorageDO.update();
        String facilityId = bo.getFacilityId();
        // 查询设备信息
        Map<String, FacilityListDO> map = facilityListMapper.nameDict(Collections.singletonList(facilityId));
        FacilityListDO facility = map.get(facilityId);
        String facilityName = "";
        if (facility != null){
            facilityName = facility.getName();
        }
        facilityPutStorageDO.setFacilityName(facilityName);
        baseMapper.insert(facilityPutStorageDO);
        // 更新库存
        Boolean stock = facilityStorageService.updateStock(facilityId, bo.getProjectId(), bo.getCount(), facilityName);
        if (!stock){
            throw new ServiceException("更新库存失败");
        }
        return true;
    }

    /**
     * 修改设备入库记录
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(FacilityPutStorageBO bo){
        FacilityPutStorageDO facilityPutStorageDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(facilityPutStorageDO)) {
            return false;
        }
        BeanCopyUtils.copy(bo,facilityPutStorageDO);
        facilityPutStorageDO.update();
        return baseMapper.updateById(facilityPutStorageDO) > 0;
    }

    /**
     * 校验并批量删除设备入库记录信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid){
        return baseMapper.deleteBatchIds(ids) > 0;
    }

}
