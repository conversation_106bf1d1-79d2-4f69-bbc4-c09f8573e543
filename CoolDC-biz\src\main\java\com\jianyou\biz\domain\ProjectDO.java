package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.jianyou.common.core.domain.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 项目信息对象 biz_project
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("biz_project")
public class ProjectDO extends BaseDO {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    private String id;
    /**
     * 项目名称
     */
    private String name;
    /**
     * 建设单位id
     */
    private Long deptId;
    /**
     * 项目地址
     */
    private String address;
    /**
     * 项目概括
     */
    private String detail;
    /**
     * 联系人
     */
    private String contacts;
    /**
     * 联系人电话
     */
    private String contactsNumber;
    /**
     * 施工开始时间
     */
    private Date startTime;
    /**
     * 施工截止时间
     */
    private Date endTime;

}
