package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.FacilityListDO;
import com.jianyou.common.core.domain.BaseOptionsVO;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.jianyou.biz.mapper.FacilityListMapper;
import com.jianyou.biz.service.IFacilityListService;
import com.jianyou.biz.domain.vo.FacilityListVO;
import com.jianyou.biz.domain.bo.FacilityListBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备清单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@RequiredArgsConstructor
@Service
public class FacilityListServiceImpl implements IFacilityListService {

    private final FacilityListMapper baseMapper;

    /**
     * 查询设备清单列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link FacilityListVO }>
     */
    @Override
    public TableDataInfo<FacilityListVO> queryPageList(FacilityListBO bo, PageQuery pageQuery) {
        IPage<FacilityListVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<FacilityListDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getName()), FacilityListDO::getName, bo.getName())
            .like(StrUtil.isNotEmpty(bo.getNumber()), FacilityListDO::getNumber, bo.getNumber())
            .eq(StrUtil.isNotEmpty(bo.getProjectId()), FacilityListDO::getProjectId, bo.getProjectId())
        );
        return TableDataInfo.build(page);
    }

    /**
     * 查询设备清单
     *
     * @param id id
     * @return {@link FacilityListVO }
     */
    @Override
    public FacilityListVO queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 新增设备清单
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean insertByBo(FacilityListBO bo) {
        FacilityListDO facilityListDO = new FacilityListDO();
        BeanCopyUtils.copy(bo, facilityListDO);
        facilityListDO.create();
        facilityListDO.update();
        return baseMapper.insert(facilityListDO) > 0;
    }

    /**
     * 修改设备清单
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    @Override
    public Boolean updateByBo(FacilityListBO bo) {
        FacilityListDO facilityListDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(facilityListDO)) {
            return false;
        }
        BeanCopyUtils.copy(bo, facilityListDO);
        facilityListDO.update();
        return baseMapper.updateById(facilityListDO) > 0;
    }

    /**
     * 校验并批量删除设备清单信息
     *
     * @param ids     主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 设备清单选项
     */
    @Override
    public List<BaseOptionsVO> option(FacilityListBO bo) {
        List<FacilityListDO> list = baseMapper.selectList(Wrappers.<FacilityListDO>lambdaQuery()
            .eq(StrUtil.isNotEmpty(bo.getProjectId()), FacilityListDO::getProjectId, bo.getProjectId())
            .select(FacilityListDO::getId, FacilityListDO::getName)
        );
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(item -> new BaseOptionsVO(item.getId(), item.getName())).collect(Collectors.toList());
    }

}
