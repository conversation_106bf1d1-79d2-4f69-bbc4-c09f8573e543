package com.jianyou.web.controller.biz;


import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jianyou.biz.domain.bo.MeterAccountLogBO;
import com.jianyou.biz.domain.vo.MeterAccountLogVO;
import com.jianyou.biz.service.IMeterAccountLogService;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 预存账户流水
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/meterAccountLog")
public class MeterAccountLogController extends BaseController {

    private final IMeterAccountLogService meterAccountLogService;

    /**
     * 查询预存账户流水列表
     */
    @SaCheckPermission("biz:meterAccountLog:list")
    @GetMapping("/list")
    public TableDataInfo<MeterAccountLogVO> list(MeterAccountLogBO bo, PageQuery pageQuery) {
        return meterAccountLogService.queryPageList(bo, pageQuery);
    }

}
