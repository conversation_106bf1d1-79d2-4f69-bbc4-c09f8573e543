package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 点位类型业务对象 biz_point_type
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PointTypeBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 字段名称
     */
    @NotBlank(message = "字段名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fieldName;


}
