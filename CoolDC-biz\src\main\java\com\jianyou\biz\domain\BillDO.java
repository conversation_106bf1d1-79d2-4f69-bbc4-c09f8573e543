package com.jianyou.biz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单对象 enms_bill
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@TableName("enms_bill")
public class BillDO {

    /**
     * ID
     */
    private String id;
    /**
     * 账单编号
     */
    private String billNo;
    /**
     * 表具id
     */
    private String meterId;
    /**
     * 表具名称
     */
    private String meterName;
    /**
     * 能源类型
     */
    private String energyType;
    /**
     * 抄表方式(0自动;1手动)
     */
    private String readingWay;
    /**
     * 上次抄表读数
     */
    private BigDecimal lastReading;
    /**
     * 上次抄表时间
     */
    private Date lastTime;
    /**
     * 本次抄表读数
     */
    private BigDecimal thisReading;
    /**
     * 本次抄表时间
     */
    private Date thisTime;
    /**
     * 费率类型(0标准;1阶梯)
     */
    private String rateType;
    /**
     * 是否分段(1是0否)
     */
    private String peakValleyFlag;
    /**
     * 能耗量
     */
    private BigDecimal energyVolume;
    /**
     * 能源单位
     */
    private String energyUnit;
    /**
     * 结算金额(元)
     */
    private BigDecimal settlementAmount;
    /**
     * 缴费状态(0未缴;1已缴)
     */
    private String payStatus;
    /**
     * 缴费方式(0现金;1线上;2预存抵扣)
     */
    private String payWay;
    /**
     * 缴费时间
     */
    private Date payTime;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 阶梯累计量 阶梯计费时存在
     */
    private BigDecimal finalCumulativeVolume;
    /**
     * 计费账期
     */
    private String cycles;
    /** 配置版本 用来检测配置是否更改 */
    private String configVersions;

}
