package com.jianyou.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.BillingConfigDO;
import com.jianyou.biz.domain.bo.BillingConfigBO;
import com.jianyou.biz.domain.bo.PeakValleyConfigBO;
import com.jianyou.biz.domain.bo.TieredConfigBO;
import com.jianyou.biz.domain.vo.BillingConfigVO;
import com.jianyou.biz.mapper.BillingConfigMapper;
import com.jianyou.biz.service.IBillingConfigService;
import com.jianyou.biz.service.IPeakValleyConfigService;
import com.jianyou.biz.service.ITieredConfigService;
import com.jianyou.common.core.domain.PageQuery;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.exception.ServiceException;
import com.jianyou.common.utils.BeanCopyUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 计费配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RequiredArgsConstructor
@Service
public class BillingConfigServiceImpl implements IBillingConfigService {

    private final BillingConfigMapper baseMapper;
    private final IPeakValleyConfigService peakValleyConfigService;
    private final ITieredConfigService tieredConfigService;

    /**
     * 查询计费配置列表
     *
     * @param bo        计费配置查询条件
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link BillingConfigVO }>
     */
    @Override
    public TableDataInfo<BillingConfigVO> queryPageList(BillingConfigBO bo, PageQuery pageQuery) {
        IPage<BillingConfigVO> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.<BillingConfigDO>lambdaQuery()
            .like(StrUtil.isNotEmpty(bo.getName()), BillingConfigDO::getName, bo.getName())
            .eq(StrUtil.isNotEmpty(bo.getEnergyType()), BillingConfigDO::getEnergyType, bo.getEnergyType())
            .eq(StrUtil.isNotEmpty(bo.getRateType()), BillingConfigDO::getRateType, bo.getRateType())
            .eq(StrUtil.isNotEmpty(bo.getPeakValleyFlag()), BillingConfigDO::getPeakValleyFlag, bo.getPeakValleyFlag())
            .orderByDesc(BillingConfigDO::getEnergyType, BillingConfigDO::getStartTime)
        );
        return TableDataInfo.build(page);
    }

    /**
     * 查询计费配置
     *
     * @param id 计费配置id
     * @return {@link BillingConfigVO }
     */
    @Override
    public BillingConfigVO queryById(String id) {
        BillingConfigVO billingConfigVO = baseMapper.selectVoById(id);
        if (billingConfigVO == null) {
            return new BillingConfigVO();
        }

        // 如果是分段计费(峰平谷)，查询分段配置
        if ("1".equals(billingConfigVO.getPeakValleyFlag())) {
            billingConfigVO.setPeakValleyConfigs(peakValleyConfigService.queryByBillingConfigId(id));
        }

        // 如果是阶梯计费，查询阶梯配置
        if ("1".equals(billingConfigVO.getRateType())) {
            billingConfigVO.setTieredConfigs(tieredConfigService.queryByBillingConfigId(id));
        }

        return billingConfigVO;
    }

    /**
     * 根据能源类型和日期查询计费配置
     *
     * @param energyType 能源类型
     * @param date       日期
     * @return {@link BillingConfigVO }
     */
    @Override
    public BillingConfigVO queryConfig(String energyType, Date date) {
        // 查询指定能源类型且日期在有效期范围内的配置
        BillingConfigVO billingConfigVO = baseMapper.selectVoOne(
            Wrappers.<BillingConfigDO>lambdaQuery()
                .eq(BillingConfigDO::getEnergyType, energyType)
                .le(BillingConfigDO::getStartTime, date)
                .ge(BillingConfigDO::getEndTime, date)
        );

        if (billingConfigVO == null) {
            return null;
        }

        // 如果是分段计费(峰平谷)，查询分段配置
        if ("1".equals(billingConfigVO.getPeakValleyFlag())) {
            billingConfigVO.setPeakValleyConfigs(peakValleyConfigService.queryByBillingConfigId(billingConfigVO.getId()));
        }

        // 如果是阶梯计费，查询阶梯配置
        if ("1".equals(billingConfigVO.getRateType())) {
            billingConfigVO.setTieredConfigs(tieredConfigService.queryByBillingConfigId(billingConfigVO.getId()));
        }

        return billingConfigVO;
    }

    /**
     * 新增计费配置
     *
     * @param bo 计费配置业务对象
     * @return {@link Boolean }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(BillingConfigBO bo) {
        // 校验时间连续性
        checkTimeConsistency(bo, null);

        // 1. 保存计费配置基本信息
        BillingConfigDO billingConfigDO = new BillingConfigDO();
        BeanCopyUtils.copy(bo, billingConfigDO);
        billingConfigDO.setConfigVersions(System.currentTimeMillis() + "");
        boolean result = baseMapper.insert(billingConfigDO) > 0;
        if (!result) {
            return false;
        }

        String billingConfigId = billingConfigDO.getId();

        // 2. 如果是分段计费(峰平谷)，保存分段配置
        if ("1".equals(bo.getPeakValleyFlag()) && CollUtil.isNotEmpty(bo.getPeakValleyConfigs())) {
            List<PeakValleyConfigBO> peakValleyConfigs = bo.getPeakValleyConfigs();
            for (PeakValleyConfigBO peakValleyConfig : peakValleyConfigs) {
                peakValleyConfig.setBillingConfigId(billingConfigId);
            }
            result = peakValleyConfigService.insertBatch(peakValleyConfigs);
            if (!result) {
                return false;
            }
        }

        // 3. 如果是阶梯计费，保存阶梯配置
        if ("1".equals(bo.getRateType()) && CollUtil.isNotEmpty(bo.getTieredConfigs())) {
            List<TieredConfigBO> tieredConfigs = bo.getTieredConfigs();
            for (TieredConfigBO tieredConfig : tieredConfigs) {
                tieredConfig.setBillingConfigId(billingConfigId);
            }
            result = tieredConfigService.insertBatch(tieredConfigs);
            return result;
        }

        return true;
    }

    /**
     * 修改计费配置
     *
     * @param bo 计费配置业务对象
     * @return {@link Boolean }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(BillingConfigBO bo) {
        // 1. 查询原有计费配置
        BillingConfigDO billingConfigDO = baseMapper.selectById(bo.getId());
        if (ObjUtil.isEmpty(billingConfigDO)) {
            return false;
        }
        Date startTime = billingConfigDO.getStartTime();
        Date endTime = billingConfigDO.getEndTime();
        // 校验时间连续性
        checkTimeConsistency(bo, billingConfigDO);

        // 2. 更新基本信息
        BeanCopyUtils.copy(bo, billingConfigDO);

        // 如果时间发生改变，则更新版本号
        if (!startTime.equals(bo.getStartTime()) || !endTime.equals(bo.getEndTime())) {
            billingConfigDO.setConfigVersions(System.currentTimeMillis() + "");
        }

        boolean result = baseMapper.updateById(billingConfigDO) > 0;
        if (!result) {
            return false;
        }

        String billingConfigId = billingConfigDO.getId();

        // 3. 如果是分段计费(峰平谷)，更新分段配置
        if ("1".equals(bo.getPeakValleyFlag())) {
            // 3.1 删除原有分段配置
            peakValleyConfigService.deleteByBillingConfigId(billingConfigId);

            // 3.2 保存新的分段配置
            if (CollUtil.isNotEmpty(bo.getPeakValleyConfigs())) {
                List<PeakValleyConfigBO> peakValleyConfigs = bo.getPeakValleyConfigs();
                for (PeakValleyConfigBO peakValleyConfig : peakValleyConfigs) {
                    peakValleyConfig.setBillingConfigId(billingConfigId);
                }
                result = peakValleyConfigService.insertBatch(peakValleyConfigs);
                if (!result) {
                    return false;
                }
            }
        } else {
            // 如果不是分段计费，删除原有分段配置
            peakValleyConfigService.deleteByBillingConfigId(billingConfigId);
        }

        // 4. 如果是阶梯计费，更新阶梯配置
        if ("1".equals(bo.getRateType())) {
            // 4.1 删除原有阶梯配置
            tieredConfigService.deleteByBillingConfigId(billingConfigId);

            // 4.2 保存新的阶梯配置
            if (CollUtil.isNotEmpty(bo.getTieredConfigs())) {
                List<TieredConfigBO> tieredConfigs = bo.getTieredConfigs();
                for (TieredConfigBO tieredConfig : tieredConfigs) {
                    tieredConfig.setBillingConfigId(billingConfigId);
                }
                result = tieredConfigService.insertBatch(tieredConfigs);
                return result;
            }
        } else {
            // 如果不是阶梯计费，删除原有阶梯配置
            tieredConfigService.deleteByBillingConfigId(billingConfigId);
        }

        return true;
    }

    /**
     * 校验并批量删除计费配置信息
     *
     * @param ids     主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }

        // 校验只能删除最后一段时间的数据
        if (isValid) {
            checkDeleteValidity(ids);
        }

        // 1. 删除关联的分段配置和阶梯配置
        for (String id : ids) {
            peakValleyConfigService.deleteByBillingConfigId(id);
            tieredConfigService.deleteByBillingConfigId(id);
        }

        // 2. 删除计费配置
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 校验时间连续性
     * 同一种能源类型下，起始有效期和截止有效期必须连续
     *
     * @param bo         当前计费配置业务对象
     * @param originalDO 原始计费配置对象（修改时使用，新增时为null）
     */
    private void checkTimeConsistency(BillingConfigBO bo, BillingConfigDO originalDO) {
        String energyType = bo.getEnergyType();
        Date startTime = bo.getStartTime();
        Date endTime = bo.getEndTime();

        if (startTime == null || endTime == null) {
            throw new ServiceException("起始有效期和截止有效期不能为空");
        }

        if (startTime.after(endTime) || startTime.equals(endTime)) {
            throw new ServiceException("起始有效期必须早于截止有效期");
        }

        // 查询同能源类型的所有配置，按起始时间排序
        List<BillingConfigDO> configList = baseMapper.selectList(
            Wrappers.<BillingConfigDO>lambdaQuery()
                .eq(BillingConfigDO::getEnergyType, energyType)
                .orderByAsc(BillingConfigDO::getStartTime)
        );

        // 如果是修改操作，需要从列表中移除当前记录
        if (originalDO != null) {
            configList.removeIf(config -> config.getId().equals(bo.getId()));
        }

        // 如果没有其他记录，则不需要校验
        if (configList.isEmpty()) {
            return;
        }

        // 查找时间上的前一条记录和后一条记录
        BillingConfigDO prevConfig = null;
        BillingConfigDO nextConfig = null;

        for (int i = 0; i < configList.size(); i++) {
            BillingConfigDO config = configList.get(i);

            // 找到第一个起始时间晚于当前记录的配置，它就是后一条记录
            if (config.getStartTime().after(startTime)) {
                nextConfig = config;
                // 如果存在前一条记录，则为前一条记录
                if (i > 0) {
                    prevConfig = configList.get(i - 1);
                }
                break;
            }

            // 如果遍历到最后一条，则当前记录是最新的，前一条是最后一条记录
            if (i == configList.size() - 1) {
                prevConfig = config;
            }
        }

        // 校验与前一条记录的连续性
        if (prevConfig != null && !startTime.equals(prevConfig.getEndTime())) {
            throw new ServiceException("当前记录的起始有效期必须等于前一条记录的截止有效期");
        }

        // 校验与后一条记录的连续性
        if (nextConfig != null && !endTime.equals(nextConfig.getStartTime())) {
            throw new ServiceException("当前记录的截止有效期必须等于后一条记录的起始有效期");
        }
    }

    /**
     * 校验删除的有效性
     * 只能删除最后一段时间的数据
     *
     * @param ids 要删除的ID集合
     */
    private void checkDeleteValidity(Collection<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        // 查询要删除的记录
        List<BillingConfigDO> toDeleteList = baseMapper.selectBatchIds(ids);
        if (CollUtil.isEmpty(toDeleteList)) {
            return;
        }

        // 按能源类型分组检查
        toDeleteList.stream()
            .map(BillingConfigDO::getEnergyType)
            .distinct()
            .forEach(energyType -> {
                // 查询该能源类型的所有记录，按起始时间降序排列
                List<BillingConfigDO> allConfigs = baseMapper.selectList(
                    Wrappers.<BillingConfigDO>lambdaQuery()
                        .eq(BillingConfigDO::getEnergyType, energyType)
                        .orderByDesc(BillingConfigDO::getStartTime)
                );

                // 要删除的该能源类型的记录
                List<String> typeDeleteIds = toDeleteList.stream()
                    .filter(config -> Objects.equals(config.getEnergyType(), energyType))
                    .map(BillingConfigDO::getId)
                    .collect(Collectors.toList());

                // 检查是否只删除最新的记录
                for (int i = 0; i < typeDeleteIds.size(); i++) {
                    if (!typeDeleteIds.contains(allConfigs.get(i).getId())) {
                        throw new ServiceException("同能源类型中只能删除最后一段时间的数据");
                    }
                }
            });
    }
}
