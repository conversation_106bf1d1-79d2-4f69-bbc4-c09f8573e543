package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jianyou.common.annotation.ExcelDictFormat;
import com.jianyou.common.convert.ExcelDictConvert;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 首页表具列表vo
 *
 * <AUTHOR>
 * @date 2023/11/03
 */
@Data
@ExcelIgnoreUnannotated
public class HomeMeterListVO {

    /** 表具id */
    private String meterId;

    /** 表具编号 */
    private String meterNumber;

    /** 表具名称 */
    @ExcelProperty(value = "表具名称")
    private String meterName;

    /** 能源类型 */
    @ExcelProperty(value = "能源类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "energy_type")
    private String energyType;

    /** 区域名称 */
    @ExcelProperty(value = "区域")
    private String areaName;

    /** 房号 */
    @ExcelProperty(value = "房号")
    private String roomNumber;

    /** 区域id */
    private String areaId;

    /** 在线状态;0离线，1在线 */
    @ExcelProperty(value = "在线状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "online_status")
    private String onlineStatus;

    /** 表具读数 */
    @ExcelProperty(value = "表具读数")
    private BigDecimal meterRecord;

    /** 今日用能 */
    @ExcelProperty(value = "今日用能")
    private BigDecimal toDayEnergy;

    /** 时间 */
    @ExcelProperty(value = "更新时间")
    private String time;


}
