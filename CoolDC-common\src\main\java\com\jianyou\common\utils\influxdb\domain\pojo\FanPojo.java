package com.jianyou.common.utils.influxdb.domain.pojo;

import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * influxdb风机对象
 *
 * <AUTHOR>
 * @date 2024/11/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Measurement(name = "fan")
public class FanPojo extends BasePojo {

    /** 温度 */
    @Column
    private String template;
    /** 压力值 */
    @Column
    private String pressure;

}
