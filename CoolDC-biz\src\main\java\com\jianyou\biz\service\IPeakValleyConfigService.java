package com.jianyou.biz.service;

import com.jianyou.biz.domain.bo.PeakValleyConfigBO;
import com.jianyou.biz.domain.vo.PeakValleyConfigVO;

import java.util.List;

/**
 * 分段配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IPeakValleyConfigService {

    /**
     * 根据计费配置ID查询分段配置列表
     *
     * @param billingConfigId 计费配置ID
     * @return 分段配置列表
     */
    List<PeakValleyConfigVO> queryByBillingConfigId(String billingConfigId);

    /**
     * 批量新增分段配置
     *
     * @param peakValleyConfigs 分段配置列表
     * @return 结果
     */
    boolean insertBatch(List<PeakValleyConfigBO> peakValleyConfigs);

    /**
     * 根据计费配置ID删除分段配置
     *
     * @param billingConfigId 计费配置ID
     * @return 结果
     */
    boolean deleteByBillingConfigId(String billingConfigId);
}
