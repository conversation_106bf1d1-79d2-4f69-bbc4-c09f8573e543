package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.QueryGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 账单详情业务对象 enms_bill_detail
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BillDetailBO extends BaseEntity {

    /**
     * ID
     */
    private String id;

    /**
     * 账单id
     */
    @NotBlank(message = "账单id不能为空", groups = { QueryGroup.class })
    private String billId;

    /**
     * 阶段名
     */
    private String phase;

    /**
     * 阶梯上限
     */
    private BigDecimal maxVolume;

    /**
     * 峰/平/谷
     */
    private String timeType;

    /**
     * 实际时间范围
     */
    private String timeRange;

    /**
     * 能耗量
     */
    private BigDecimal volume;
    /**
     * 能源单位
     */
    private String energyUnit;
    /**
     * 基价
     */
    private BigDecimal basePrice;
    /**
     * 加价（峰/谷加价）
     */
    private BigDecimal addPrice;
    /**
     * 单价（基价+加价）
     */
    private BigDecimal unitPrice;

    /**
     * 费用
     */
    private BigDecimal fee;


}
