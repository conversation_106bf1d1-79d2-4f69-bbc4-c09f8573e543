package com.jianyou.web.controller.biz;

import com.jianyou.biz.domain.bo.HomeBO;
import com.jianyou.biz.domain.vo.HomeMeterDataVO;
import com.jianyou.biz.domain.vo.HomeMeterListVO;
import com.jianyou.biz.domain.vo.HomeMeterOnLineStatusVO;
import com.jianyou.biz.service.IHomeService;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 首页控制器
 *
 * <AUTHOR>
 * @date 2023/11/03
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/biz/home")
public class HomeController extends BaseController {

    private final IHomeService homeService;


    /**
     * 表具列表
     */
    @GetMapping("/subList")
    public R<HomeMeterDataVO> subList(HomeBO bo) {
        return R.ok(homeService.subList(bo));
    }


    /**
     * 表具在线情况
     */
    @GetMapping("/meterOnlineStatus")
    public R<HomeMeterOnLineStatusVO> meterOnlineStatus(HomeBO bo) {
        return R.ok(homeService.meterOnlineStatus(bo));
    }

    /**
     * 导出表具数据
     */
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeBO bo) {
        HomeMeterDataVO homeMeterDataVO = homeService.subList(bo);
        List<List<HomeMeterListVO>> meterSubList = homeMeterDataVO.getMeterSubList();
        // 转为一个集合
        List<HomeMeterListVO> meterList = meterSubList.stream().flatMap(List::stream).collect(Collectors.toList());
        ExcelUtil.exportExcel(meterList, "表具能耗", HomeMeterListVO.class, response);
    }

}
