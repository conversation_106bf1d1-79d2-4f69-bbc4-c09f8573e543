package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 项目验收业务对象 biz_project_check
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectCheckBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 验收报告（上传附件）
     */
    @NotBlank(message = "验收报告不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reportOssIds;

    /**
     * 验收资料（图纸、单据）
     */
    @NotBlank(message = "验收资料不能为空", groups = { AddGroup.class, EditGroup.class })
    private String dataOssIds;

    /**
     * 验收人
     */
    @NotBlank(message = "验收人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String acceptor;

    /**
     * 验收时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "验收时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date receiveTime;

    /**
     * 项目id
     */
    @NotBlank(message = "项目不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectId;


}
