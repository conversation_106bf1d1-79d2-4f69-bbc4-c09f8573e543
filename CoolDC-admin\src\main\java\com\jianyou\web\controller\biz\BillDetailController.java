package com.jianyou.web.controller.biz;


import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jianyou.biz.domain.bo.BillDetailBO;
import com.jianyou.biz.domain.vo.BillDetailVO;
import com.jianyou.biz.service.IBillDetailService;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.QueryGroup;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 账单详情
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/billDetail")
public class BillDetailController extends BaseController {

    private final IBillDetailService billDetailService;

    /**
     * 查询账单详情列表
     */
    @SaCheckPermission("biz:billDetail:list")
    @GetMapping("/list")
    public R<List<BillDetailVO>> list(@Validated(QueryGroup.class) BillDetailBO bo) {
        return R.ok(billDetailService.queryList(bo));
    }
}
