package com.jianyou.web.controller.biz;

import com.jianyou.biz.domain.bo.EnergySummaryBO;
import com.jianyou.biz.domain.bo.MeterAnalyseBO;
import com.jianyou.biz.domain.vo.EnergyAnalyseVO;
import com.jianyou.biz.service.IMeterAnalyseService;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.QueryGroup;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 仪表分析控制器
 *
 * <AUTHOR>
 * @date 2025/07/02
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/meterAnalyse")
public class MeterAnalyseController {

    private final IMeterAnalyseService meterAnalyseService;

    /**
     * 能耗分析
     */
    @GetMapping("/energyAnalyse")
    public R<EnergyAnalyseVO> energyAnalyse(@Validated(QueryGroup.class) MeterAnalyseBO bo) {
        return R.ok(meterAnalyseService.energyAnalyse(bo));
    }

    /**
     * 能耗总汇
     */
    @GetMapping("/energySummary")
    public R<Double> energySummary(@Validated(QueryGroup.class) EnergySummaryBO bo) {
        return R.ok(meterAnalyseService.energySummary(bo));
    }

}
