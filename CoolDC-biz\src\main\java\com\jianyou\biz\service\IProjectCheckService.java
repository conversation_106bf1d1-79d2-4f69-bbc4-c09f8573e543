package com.jianyou.biz.service;

import com.jianyou.biz.domain.vo.ProjectCheckVO;
import com.jianyou.biz.domain.bo.ProjectCheckBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.Collection;

/**
 * 项目验收Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IProjectCheckService {


    /**
     * 查询项目验收列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link ProjectCheckVO }>
     */
    TableDataInfo<ProjectCheckVO> queryPageList(ProjectCheckBO bo, PageQuery pageQuery);

    /**
     * 查询项目验收
     *
     * @param id id
     * @return {@link ProjectCheckVO }
     */
     ProjectCheckVO queryById(String id);

    /**
     * 新增项目验收
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(ProjectCheckBO bo);

    /**
     * 修改项目验收
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(ProjectCheckBO bo);

    /**
     * 校验并批量删除项目验收信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
