package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jianyou.common.annotation.ExcelDictFormat;
import com.jianyou.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单视图对象 enms_bill
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@ExcelIgnoreUnannotated
public class BillVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private String id;

    /**
     * 账单编号
     */
    @ExcelProperty(value = "账单编号")
    private String billNo;

    /**
     * 表具id
     */
    private String meterId;

    /**
     * 表具名称
     */
    @ExcelProperty(value = "表具名称")
    private String meterName;

    /**
     * 能源类型
     */
    @ExcelProperty(value = "能源类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "energy_type")
    private String energyType;

    /**
     * 抄表方式(0自动;1手动)
     */
    private String readingWay;

    /**
     * 上次抄表读数
     */
    @ExcelProperty(value = "上次抄表读数")
    private BigDecimal lastReading;

    /**
     * 上次抄表时间
     */
    @ExcelProperty(value = "上次抄表时间")
    private Date lastTime;

    /**
     * 本次抄表读数
     */
    @ExcelProperty(value = "本次抄表读数")
    private BigDecimal thisReading;

    /**
     * 本次抄表时间
     */
    @ExcelProperty(value = "本次抄表时间")
    private Date thisTime;

    /**
     * 计费类型(0标准;1阶梯)
     */
    @ExcelProperty(value = "计费类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "bill_rate_type")
    private String rateType;

    /**
     * 峰平谷(1是0否)
     */
    @ExcelProperty(value = "峰平谷", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "yes_no")
    private String peakValleyFlag;

    /**
     * 能耗量
     */
    @ExcelProperty(value = "能耗量")
    private BigDecimal energyVolume;

    /**
     * 能源单位
     */
    @ExcelProperty(value = "能源单位")
    private String energyUnit;

    /**
     * 结算金额(元)
     */
    @ExcelProperty(value = "结算金额(元)")
    private BigDecimal settlementAmount;

    /**
     * 缴费状态(0未缴;1已缴)
     */
    @ExcelProperty(value = "缴费状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "pay_status")
    private String payStatus;

    /**
     * 缴费方式(0现金;1线上;2预存抵扣)
     */
    @ExcelProperty(value = "缴费方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "pay_way")
    private String payWay;

    /**
     * 缴费时间
     */
    @ExcelProperty(value = "缴费时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "账单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 阶梯累计量 阶梯计费时存在
     */
    private BigDecimal finalCumulativeVolume;
    /**
     * 计费周期
     */
    private String cycles;
    /** 配置版本 用来检测配置是否更改 */
    private String configVersions;


}
