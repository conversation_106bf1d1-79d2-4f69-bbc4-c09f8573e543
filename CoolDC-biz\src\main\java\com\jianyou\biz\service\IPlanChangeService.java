package com.jianyou.biz.service;

import com.jianyou.biz.domain.vo.PlanChangeVO;
import com.jianyou.biz.domain.bo.PlanChangeBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.Collection;

/**
 * 计划变更Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IPlanChangeService {


    /**
     * 查询计划变更列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link PlanChangeVO }>
     */
    TableDataInfo<PlanChangeVO> queryPageList(PlanChangeBO bo, PageQuery pageQuery);

    /**
     * 查询计划变更
     *
     * @param id id
     * @return {@link PlanChangeVO }
     */
     PlanChangeVO queryById(String id);

    /**
     * 新增计划变更
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(PlanChangeBO bo);

    /**
     * 修改计划变更
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(PlanChangeBO bo);

    /**
     * 校验并批量删除计划变更信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
