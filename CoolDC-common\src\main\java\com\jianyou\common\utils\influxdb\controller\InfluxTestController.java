package com.jianyou.common.utils.influxdb.controller;

import cn.hutool.core.date.DateUtil;
import com.jianyou.common.enums.InfluxdbField;
import com.jianyou.common.enums.InfluxdbMeasurement;
import com.jianyou.common.utils.influxdb.InfluxUtils;
import com.jianyou.common.utils.influxdb.domain.dto.WindowAggregationResult;
import com.jianyou.common.utils.influxdb.test.WindowAggregationTest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * InfluxDB时间开窗聚合测试控制器
 *
 * <AUTHOR> Assistant
 * @date 2025/01/02
 */
@Slf4j
@RestController
@RequestMapping("/api/influx/test")
@RequiredArgsConstructor
public class InfluxTestController {

    private final InfluxUtils influxUtils;
    private final WindowAggregationTest windowAggregationTest;

    /**
     * 执行所有测试
     */
    @GetMapping("/run-all")
    public Map<String, Object> runAllTests() {
        Map<String, Object> result = new HashMap<>();
        try {
            windowAggregationTest.runAllTests();
            result.put("success", true);
            result.put("message", "所有测试执行完成，请查看日志");
        } catch (Exception e) {
            log.error("测试执行失败", e);
            result.put("success", false);
            result.put("message", "测试执行失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 测试按天聚合体积数据（对应您的Flux查询）
     */
    @GetMapping("/daily-volume")
    public Map<String, Object> testDailyVolumeAggregation() {
        Map<String, Object> result = new HashMap<>();
        try {
            windowAggregationTest.testDailyVolumeAggregation();
            result.put("success", true);
            result.put("message", "按天聚合测试完成，请查看日志");
        } catch (Exception e) {
            log.error("按天聚合测试失败", e);
            result.put("success", false);
            result.put("message", "测试失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 自定义参数测试时间开窗聚合
     */
    @PostMapping("/custom-aggregation")
    public Map<String, Object> customAggregationTest(
            @RequestParam(defaultValue = "METER_VOLUME") String measurement,
            @RequestParam(defaultValue = "VOLUME") String field,
            @RequestParam(defaultValue = "7") int daysBack,
            @RequestParam(defaultValue = "1d") String windowDuration,
            @RequestParam(required = false) List<String> tags) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            // 参数转换
            InfluxdbMeasurement measurementEnum = InfluxdbMeasurement.valueOf(measurement);
            InfluxdbField fieldEnum = InfluxdbField.valueOf(field);
            Date startTime = DateUtil.offsetDay(new Date(), -daysBack);
            Date endTime = new Date();
            
            // 执行查询
            List<WindowAggregationResult> results = influxUtils.selectWindowAggregation(
                    measurementEnum,
                    tags,
                    fieldEnum,
                    startTime,
                    endTime,
                    windowDuration,
                    null
            );
            
            result.put("success", true);
            result.put("message", "查询成功");
            result.put("resultCount", results.size());
            result.put("data", results);
            
            // 记录日志
            log.info("自定义聚合查询完成: measurement={}, field={}, daysBack={}, windowDuration={}, resultCount={}", 
                    measurement, field, daysBack, windowDuration, results.size());
            
        } catch (Exception e) {
            log.error("自定义聚合查询失败", e);
            result.put("success", false);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取生成的Flux查询语句（用于调试）
     */
    @PostMapping("/get-flux-query")
    public Map<String, Object> getFluxQuery(
            @RequestParam(defaultValue = "METER_VOLUME") String measurement,
            @RequestParam(defaultValue = "VOLUME") String field,
            @RequestParam(defaultValue = "7") int daysBack,
            @RequestParam(defaultValue = "1d") String windowDuration,
            @RequestParam(required = false) List<String> tags) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            // 参数转换
            InfluxdbMeasurement measurementEnum = InfluxdbMeasurement.valueOf(measurement);
            InfluxdbField fieldEnum = InfluxdbField.valueOf(field);
            Date startTime = DateUtil.offsetDay(new Date(), -daysBack);
            Date endTime = new Date();
            
            // 通过反射获取私有方法来生成查询语句（仅用于调试）
            java.lang.reflect.Method method = InfluxUtils.class.getDeclaredMethod(
                    "buildWindowAggregationQuery",
                    InfluxdbMeasurement.class,
                    List.class,
                    InfluxdbField.class,
                    Date.class,
                    Date.class,
                    String.class,
                    List.class
            );
            method.setAccessible(true);
            
            String fluxQuery = (String) method.invoke(influxUtils, 
                    measurementEnum, tags, fieldEnum, startTime, endTime, windowDuration, null);
            
            result.put("success", true);
            result.put("fluxQuery", fluxQuery);
            result.put("parameters", Map.of(
                    "measurement", measurement,
                    "field", field,
                    "startTime", DateUtil.formatDateTime(startTime),
                    "endTime", DateUtil.formatDateTime(endTime),
                    "windowDuration", windowDuration,
                    "tags", tags
            ));
            
        } catch (Exception e) {
            log.error("获取Flux查询语句失败", e);
            result.put("success", false);
            result.put("message", "获取查询语句失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 健康检查 - 测试InfluxDB连接
     */
    @GetMapping("/health")
    public Map<String, Object> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 尝试执行一个简单的查询来测试连接
            List<WindowAggregationResult> testResults = influxUtils.selectWindowAggregation(
                    InfluxdbMeasurement.METER_VOLUME,
                    null,
                    InfluxdbField.VOLUME,
                    DateUtil.offsetMinute(new Date(), -5), // 最近5分钟
                    new Date(),
                    "1m",
                    null
            );
            
            result.put("success", true);
            result.put("message", "InfluxDB连接正常");
            result.put("testResultCount", testResults.size());
            
        } catch (Exception e) {
            log.error("InfluxDB连接测试失败", e);
            result.put("success", false);
            result.put("message", "InfluxDB连接失败: " + e.getMessage());
        }
        return result;
    }
}
