package com.jianyou.web.controller.biz;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import com.jianyou.biz.domain.bo.AreaBO;
import com.jianyou.biz.domain.vo.AreaVO;
import com.jianyou.biz.service.IAreaService;
import com.jianyou.common.annotation.RepeatSubmit;
import com.jianyou.common.core.controller.BaseController;
import com.jianyou.common.core.domain.R;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 区域
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/biz/area")
public class AreaController extends BaseController {

    private final IAreaService areaService;

    /**
     * 查询区域列表
     */
    @SaCheckPermission("biz:area:list")
    @GetMapping("/list")
    public R<List<Tree<String>>> list(AreaBO bo) {
        return R.ok(areaService.queryPageList(bo));
    }

    /**
     * 获取区域详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:area:query")
    @GetMapping("/{id}")
    public R<AreaVO> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable String id) {
        return R.ok(areaService.queryById(id));
    }

    /**
     * 新增区域
     */
    @SaCheckPermission("biz:area:add")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AreaBO bo) {
        return toAjax(areaService.insertByBo(bo));
    }

    /**
     * 修改区域
     */
    @SaCheckPermission("biz:area:edit")
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AreaBO bo) {
        return toAjax(areaService.updateByBo(bo));
    }

    /**
     * 删除区域
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:area:remove")
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return toAjax(areaService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
