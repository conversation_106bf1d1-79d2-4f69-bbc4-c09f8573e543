package com.jianyou.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jianyou.biz.domain.FacilityGetReadingDO;
import com.jianyou.biz.domain.bo.FacilityGetReadingBO;
import com.jianyou.biz.domain.vo.FacilityGetReadingVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.jianyou.biz.mapper.FacilityGetReadingMapper;
import com.jianyou.biz.service.IFacilityGetReadingService;

import java.util.List;

/**
 * 设备请领记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@RequiredArgsConstructor
@Service
public class FacilityGetReadingServiceImpl implements IFacilityGetReadingService {

    private final FacilityGetReadingMapper baseMapper;

    /**
     * 查询设备请领记录列表
     */
    @Override
    public List<FacilityGetReadingVO> queryList(FacilityGetReadingBO bo){
        return baseMapper.selectVoList(Wrappers.<FacilityGetReadingDO>lambdaQuery()
            .eq(StrUtil.isNotEmpty(bo.getFacilityGetStorageId()), FacilityGetReadingDO::getFacilityGetStorageId, bo.getFacilityGetStorageId())
        );
    }

}
