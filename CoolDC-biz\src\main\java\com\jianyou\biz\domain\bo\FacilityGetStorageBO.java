package com.jianyou.biz.domain.bo;

import com.jianyou.common.core.domain.BaseEntity;
import com.jianyou.common.core.validate.AddGroup;
import com.jianyou.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 设备请领业务对象 biz_facility_get_storage
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FacilityGetStorageBO extends BaseEntity {

    /**
     * ID
     */
    @NotBlank(message = "ID不能为空", groups = { EditGroup.class })
    private String id;

    /**
     * 请领人
     */
    @NotBlank(message = "请领人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String getPrincipal;

    /**
     * 描述
     */
    @NotBlank(message = "描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String details;

    /**
     * 请领时间
     */
    @NotNull(message = "请领时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date drawTime;

    /**
     * 项目id
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotBlank(message = "项目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String projectId;

    private List<FacilityGetReadingBO> facilityDetails;

}
