package com.jianyou.biz.service;

import com.jianyou.biz.domain.vo.FacilityStorageVO;
import com.jianyou.biz.domain.bo.FacilityStorageBO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 设备库存Service接口
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface IFacilityStorageService {


    /**
     * 查询设备库存列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link FacilityStorageVO }>
     */
    TableDataInfo<FacilityStorageVO> queryPageList(FacilityStorageBO bo, PageQuery pageQuery);

    /**
     * 查询设备库存列表All
     */
    List<FacilityStorageVO> listAll(FacilityStorageBO bo);

    /**
     * 查询设备库存
     *
     * @param id id
     * @return {@link FacilityStorageVO }
     */
     FacilityStorageVO queryById(String id);

    /**
     * 新增设备库存
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(FacilityStorageBO bo);

    /**
     * 修改设备库存
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(FacilityStorageBO bo);

    /**
     * 校验并批量删除设备库存信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 通用库存更新方法
     *
     * @param facilityId 设备ID
     * @param projectId 项目ID
     * @param stockChange 库存变化量（正数为入库，负数为出库）
     * @param facilityName 设备名称（可选，为空时自动查询）
     * @return {@link Boolean} 更新是否成功
     */
    Boolean updateStock(String facilityId, String projectId, BigDecimal stockChange, String facilityName);

}
