package com.jianyou.demo.mapper;

import com.jianyou.common.annotation.DataColumn;
import com.jianyou.common.annotation.DataPermission;
import com.jianyou.common.core.mapper.BaseMapperPlus;
import com.jianyou.demo.domain.TestTree;
import com.jianyou.demo.domain.vo.TestTreeVo;

/**
 * 测试树表Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-26
 */
@DataPermission({
    @DataColumn(key = "deptName", value = "dept_id"),
    @DataColumn(key = "userName", value = "user_id")
})
public interface TestTreeMapper extends BaseMapperPlus<TestTreeMapper, TestTree, TestTreeVo> {

}
