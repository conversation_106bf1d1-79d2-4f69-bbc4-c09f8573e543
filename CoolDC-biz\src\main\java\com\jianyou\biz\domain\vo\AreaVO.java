package com.jianyou.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 区域视图对象 enms_area
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@ExcelIgnoreUnannotated
public class AreaVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 父级区域ID
     */
    @ExcelProperty(value = "父级区域ID")
    private String parentId;

    /**
     * 同级区域排序
     */
    @ExcelProperty(value = "同级区域排序")
    private Long weight;


}
