package com.jianyou.biz.service;

import com.jianyou.biz.domain.vo.FacilityListVO;
import com.jianyou.biz.domain.bo.FacilityListBO;
import com.jianyou.common.core.domain.BaseOptionsVO;
import com.jianyou.common.core.page.TableDataInfo;
import com.jianyou.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 设备清单Service接口
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IFacilityListService {


    /**
     * 查询设备清单列表
     *
     * @param bo        bo
     * @param pageQuery 分页条件
     * @return {@link TableDataInfo }<{@link FacilityListVO }>
     */
    TableDataInfo<FacilityListVO> queryPageList(FacilityListBO bo, PageQuery pageQuery);

    /**
     * 查询设备清单
     *
     * @param id id
     * @return {@link FacilityListVO }
     */
     FacilityListVO queryById(String id);

    /**
     * 新增设备清单
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean insertByBo(FacilityListBO bo);

    /**
     * 修改设备清单
     *
     * @param bo bo
     * @return {@link Boolean }
     */
    Boolean updateByBo(FacilityListBO bo);

    /**
     * 校验并批量删除设备清单信息
     *
     * @param ids 主键串
     * @param isValid 是否校验
     * @return {@link Boolean }
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 设备清单选项
     */
    List<BaseOptionsVO> option(FacilityListBO bo);
}
